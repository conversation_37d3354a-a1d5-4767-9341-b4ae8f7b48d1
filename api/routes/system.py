"""
System management endpoints.

This module provides system information, configuration,
and management capabilities.
"""

from typing import Dict, Any
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel

from ..models import (
    SystemInfoResponse,
    AgentConfigUpdate,
    ModelInfo,
    SessionInfo,
    ToolUsageStats
)
from ..app import get_agent, get_vector_store, get_tool_registry, get_logger
from ...agent import AgentConfig
from ...providers import get_llm_provider, get_embedding_provider

router = APIRouter()
logger = get_logger(__name__)


@router.get("/system/info", response_model=SystemInfoResponse)
async def get_system_info(
    agent=Depends(get_agent),
    vector_store=Depends(get_vector_store)
):
    """
    Get system information and capabilities.
    
    Returns comprehensive information about the agent system including:
    - Agent configuration
    - Available tools
    - Model information
    - Storage statistics
    """
    try:
        # Get agent info
        agent_config = agent.config
        
        # Get LLM provider info
        llm_provider = get_llm_provider()
        llm_info = ModelInfo(
            provider=llm_provider.__class__.__name__.replace("Provider", ""),
            model=agent_config.model.name,
            capabilities=["chat", "streaming", "tools"],
            context_window=agent_config.model.context_window,
            max_output_tokens=agent_config.model.max_tokens
        )
        
        # Get embedding provider info
        embedding_provider = await get_embedding_provider()
        embedding_info = ModelInfo(
            provider=embedding_provider.__class__.__name__.replace("Provider", ""),
            model=getattr(embedding_provider, "model", "unknown"),
            capabilities=["embeddings"],
            context_window=0,  # Not applicable for embeddings
            max_output_tokens=None
        )
        
        # Get available tools
        tool_registry = get_tool_registry()
        available_tools = list(tool_registry.tools.keys())
        
        # Get storage stats
        storage_stats = await vector_store.get_statistics()
        
        return SystemInfoResponse(
            agent_name=agent_config.name,
            agent_version="1.0.0",
            available_tools=available_tools,
            llm_model=llm_info,
            embedding_model=embedding_info,
            storage_backend=vector_store.__class__.__name__,
            document_count=storage_stats.get("document_count", 0),
            chunk_count=storage_stats.get("chunk_count", 0)
        )
    
    except Exception as e:
        logger.error(f"System info error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get system information"
        )


@router.patch("/system/config")
async def update_agent_config(
    config_update: AgentConfigUpdate,
    agent=Depends(get_agent)
):
    """
    Update agent configuration dynamically.
    
    Allows updating certain agent configuration parameters
    without restarting the system.
    """
    try:
        updates = {}
        
        # Update temperature
        if config_update.temperature is not None:
            agent.config.model.temperature = config_update.temperature
            updates["temperature"] = config_update.temperature
        
        # Update max tokens
        if config_update.max_tokens is not None:
            agent.config.model.max_tokens = config_update.max_tokens
            updates["max_tokens"] = config_update.max_tokens
        
        # Update search limit
        if config_update.search_limit is not None:
            agent.config.search.default_limit = config_update.search_limit
            updates["search_limit"] = config_update.search_limit
        
        # Update streaming
        if config_update.enable_streaming is not None:
            agent.config.model.enable_streaming = config_update.enable_streaming
            updates["enable_streaming"] = config_update.enable_streaming
        
        # Update system prompt
        if config_update.system_prompt is not None:
            agent.config.prompts.system_prompt = config_update.system_prompt
            updates["system_prompt"] = "Updated"
        
        return {
            "status": "success",
            "message": "Configuration updated",
            "updates": updates
        }
    
    except Exception as e:
        logger.error(f"Config update error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update configuration"
        )


@router.get("/system/sessions")
async def list_active_sessions(
    agent=Depends(get_agent)
):
    """
    List active chat sessions.
    
    Returns information about currently active sessions
    including message counts and tool usage.
    """
    try:
        # Get active sessions
        sessions = await agent.get_active_sessions()
        
        session_infos = []
        for session in sessions:
            # Get session statistics
            stats = await agent.get_session_statistics(session["session_id"])
            
            # Format tool usage
            tool_usage = [
                ToolUsageStats(
                    tool_name=tool_name,
                    total_calls=usage["total"],
                    successful_calls=usage["successful"],
                    failed_calls=usage["failed"],
                    avg_execution_time=usage["avg_time"]
                )
                for tool_name, usage in stats.get("tool_usage", {}).items()
            ]
            
            session_info = SessionInfo(
                session_id=session["session_id"],
                message_count=session["message_count"],
                created_at=session["created_at"],
                last_activity=session["last_activity"],
                tool_usage=tool_usage
            )
            session_infos.append(session_info)
        
        return {
            "sessions": session_infos,
            "total_sessions": len(session_infos)
        }
    
    except Exception as e:
        logger.error(f"Session list error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list sessions"
        )


@router.post("/system/tools/reload")
async def reload_tools(
    tool_registry=Depends(get_tool_registry)
):
    """
    Reload tool registry.
    
    Useful for dynamically updating available tools
    without restarting the system.
    """
    try:
        # Reload tools
        tool_registry.reload()
        
        # Get updated tool list
        available_tools = list(tool_registry.tools.keys())
        
        return {
            "status": "success",
            "message": "Tools reloaded",
            "available_tools": available_tools
        }
    
    except Exception as e:
        logger.error(f"Tool reload error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reload tools"
        )


@router.post("/system/cache/clear")
async def clear_caches():
    """
    Clear various system caches.
    
    Clears embedding cache and other temporary data.
    """
    try:
        cleared = []
        
        # Clear embedding cache
        embedding_provider = await get_embedding_provider()
        if hasattr(embedding_provider, "clear_cache"):
            embedding_provider.clear_cache()
            cleared.append("embeddings")
        
        # Clear search cache if applicable
        # Add other cache clearing as needed
        
        return {
            "status": "success",
            "message": "Caches cleared",
            "cleared_caches": cleared
        }
    
    except Exception as e:
        logger.error(f"Cache clear error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clear caches"
        )


@router.get("/system/metrics")
async def get_system_metrics():
    """
    Get system performance metrics.
    
    Returns various performance and usage metrics.
    """
    try:
        import psutil
        import os
        
        # Get system metrics
        process = psutil.Process(os.getpid())
        
        metrics = {
            "cpu_percent": process.cpu_percent(interval=1),
            "memory_usage_mb": process.memory_info().rss / 1024 / 1024,
            "open_files": len(process.open_files()),
            "num_threads": process.num_threads(),
            "uptime_seconds": (datetime.now() - datetime.fromtimestamp(process.create_time())).total_seconds()
        }
        
        # Add custom metrics as needed
        
        return {
            "timestamp": datetime.now(),
            "metrics": metrics
        }
    
    except Exception as e:
        logger.error(f"Metrics error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get metrics"
        )


@router.post("/system/shutdown")
async def shutdown_system():
    """
    Gracefully shutdown the system.
    
    This endpoint should be protected in production.
    """
    try:
        logger.info("System shutdown requested")
        
        # Perform cleanup tasks
        # Note: In production, this should be handled by the process manager
        
        return {
            "status": "success",
            "message": "Shutdown initiated"
        }
    
    except Exception as e:
        logger.error(f"Shutdown error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initiate shutdown"
        )