"""
Document management endpoints.

This module handles document upload, processing, and management
for the RAG system.
"""

import os
import uuid
from typing import List, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from fastapi.responses import JSONResponse

from ..models import (
    DocumentUploadRequest,
    DocumentUploadResponse,
    DocumentInfo,
    DocumentListResponse,
    DocumentQueryRequest
)
from ..app import get_vector_store, get_logger
from ...rag import DocumentIngestionPipeline, create_chunker
from ...providers import get_embedding_provider
from ...utils import validate_file_name, sanitize_input

router = APIRouter()
logger = get_logger(__name__)

# Allowed file extensions
ALLOWED_EXTENSIONS = ["txt", "pdf", "md", "json", "csv", "docx"]
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB


@router.post("/documents/upload", response_model=DocumentUploadResponse)
async def upload_document(
    file: UploadFile = File(...),
    title: str = Form(...),
    source: str = Form(...),
    metadata: Optional[str] = Form(None),
    process_immediately: bool = Form(True),
    vector_store=Depends(get_vector_store)
):
    """
    Upload a document for processing.
    
    Args:
        file: Document file to upload
        title: Document title
        source: Document source/URL
        metadata: Optional JSON metadata
        process_immediately: Whether to process immediately
    """
    # Validate file
    if not validate_file_name(file.filename, ALLOWED_EXTENSIONS):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid file type. Allowed: {', '.join(ALLOWED_EXTENSIONS)}"
        )
    
    # Check file size
    content = await file.read()
    if len(content) > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File too large. Maximum size: {MAX_FILE_SIZE} bytes"
        )
    
    # Reset file position
    await file.seek(0)
    
    try:
        # Generate document ID
        document_id = str(uuid.uuid4())
        
        # Parse metadata if provided
        doc_metadata = {}
        if metadata:
            import json
            try:
                doc_metadata = json.loads(metadata)
            except json.JSONDecodeError:
                logger.warning("Invalid metadata JSON, ignoring")
        
        # Add standard metadata
        doc_metadata.update({
            "title": sanitize_input(title),
            "source": sanitize_input(source),
            "filename": file.filename,
            "uploaded_at": datetime.now().isoformat()
        })
        
        if process_immediately:
            # Process document
            start_time = datetime.now()
            
            # Save temporary file
            temp_path = f"/tmp/{document_id}_{file.filename}"
            with open(temp_path, "wb") as f:
                f.write(content)
            
            try:
                # Create ingestion pipeline
                pipeline = DocumentIngestionPipeline(
                    chunker=create_chunker(
                        strategy=os.getenv("CHUNKING_STRATEGY", "sliding_window")
                    ),
                    embedding_generator=await get_embedding_provider()
                )
                
                # Process document
                processed_doc = await pipeline.ingest_file(
                    file_path=temp_path,
                    document_id=document_id,
                    metadata=doc_metadata
                )
                
                # Store in vector database
                chunk_count = await vector_store.store_document(processed_doc)
                
                processing_time = (datetime.now() - start_time).total_seconds()
                
                return DocumentUploadResponse(
                    document_id=document_id,
                    status="completed",
                    message="Document processed successfully",
                    chunk_count=chunk_count,
                    processing_time=processing_time
                )
            
            finally:
                # Clean up temp file
                if os.path.exists(temp_path):
                    os.remove(temp_path)
        
        else:
            # Queue for processing (implement based on your queue system)
            logger.info(f"Document {document_id} queued for processing")
            
            return DocumentUploadResponse(
                document_id=document_id,
                status="processing",
                message="Document queued for processing"
            )
    
    except Exception as e:
        logger.error(f"Document upload error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process document"
        )


@router.post("/documents/upload-url", response_model=DocumentUploadResponse)
async def upload_document_from_url(
    request: DocumentUploadRequest,
    vector_store=Depends(get_vector_store)
):
    """
    Upload a document from URL or with text content.
    
    This endpoint allows uploading documents by providing the content directly
    or specifying a URL to fetch from.
    """
    try:
        # Generate document ID
        document_id = str(uuid.uuid4())
        
        # Prepare metadata
        doc_metadata = request.metadata or {}
        doc_metadata.update({
            "title": request.title,
            "source": request.source,
            "uploaded_at": datetime.now().isoformat()
        })
        
        if request.process_immediately:
            # Process document
            start_time = datetime.now()
            
            # Create ingestion pipeline
            pipeline = DocumentIngestionPipeline(
                chunker=create_chunker(),
                embedding_generator=await get_embedding_provider()
            )
            
            # Process document from text
            from ...rag.ingestion import Document
            document = Document(
                document_id=document_id,
                content=request.content,
                metadata=doc_metadata
            )
            
            processed_doc = await pipeline.process_document(document)
            
            # Store in vector database
            chunk_count = await vector_store.store_document(processed_doc)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return DocumentUploadResponse(
                document_id=document_id,
                status="completed",
                message="Document processed successfully",
                chunk_count=chunk_count,
                processing_time=processing_time
            )
        
        else:
            # Queue for processing
            logger.info(f"Document {document_id} queued for processing")
            
            return DocumentUploadResponse(
                document_id=document_id,
                status="processing",
                message="Document queued for processing"
            )
    
    except Exception as e:
        logger.error(f"Document upload error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process document"
        )


@router.get("/documents", response_model=DocumentListResponse)
async def list_documents(
    page: int = 1,
    per_page: int = 20,
    vector_store=Depends(get_vector_store)
):
    """
    List all documents in the system.
    
    Args:
        page: Page number (1-based)
        per_page: Items per page
    """
    try:
        # Validate pagination
        from ...utils import validate_pagination
        offset, limit = validate_pagination(page, per_page)
        
        # Get documents from vector store
        documents = await vector_store.list_documents(
            offset=offset,
            limit=limit
        )
        
        # Get total count
        total_count = await vector_store.count_documents()
        
        # Convert to response format
        doc_infos = [
            DocumentInfo(
                document_id=doc["document_id"],
                title=doc["metadata"].get("title", "Untitled"),
                source=doc["metadata"].get("source", "Unknown"),
                chunk_count=doc["chunk_count"],
                created_at=datetime.fromisoformat(doc["metadata"]["uploaded_at"]),
                metadata=doc["metadata"]
            )
            for doc in documents
        ]
        
        return DocumentListResponse(
            documents=doc_infos,
            total_count=total_count,
            page=page,
            per_page=per_page
        )
    
    except Exception as e:
        logger.error(f"Document list error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list documents"
        )


@router.get("/documents/{document_id}", response_model=DocumentInfo)
async def get_document(
    document_id: str,
    vector_store=Depends(get_vector_store)
):
    """
    Get information about a specific document.
    
    Args:
        document_id: Document ID
    """
    try:
        # Get document info
        doc_info = await vector_store.get_document_info(document_id)
        
        if not doc_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Document {document_id} not found"
            )
        
        return DocumentInfo(
            document_id=doc_info["document_id"],
            title=doc_info["metadata"].get("title", "Untitled"),
            source=doc_info["metadata"].get("source", "Unknown"),
            chunk_count=doc_info["chunk_count"],
            created_at=datetime.fromisoformat(doc_info["metadata"]["uploaded_at"]),
            metadata=doc_info["metadata"]
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Document get error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get document"
        )


@router.delete("/documents/{document_id}")
async def delete_document(
    document_id: str,
    vector_store=Depends(get_vector_store)
):
    """
    Delete a document and all its chunks.
    
    Args:
        document_id: Document ID to delete
    """
    try:
        # Check if document exists
        doc_info = await vector_store.get_document_info(document_id)
        
        if not doc_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Document {document_id} not found"
            )
        
        # Delete document
        deleted_count = await vector_store.delete_document(document_id)
        
        return {
            "status": "success",
            "message": f"Document {document_id} deleted",
            "chunks_deleted": deleted_count
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Document delete error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete document"
        )


@router.post("/documents/{document_id}/reprocess")
async def reprocess_document(
    document_id: str,
    vector_store=Depends(get_vector_store)
):
    """
    Reprocess a document to update embeddings.
    
    Args:
        document_id: Document ID to reprocess
    """
    try:
        # Get document info
        doc_info = await vector_store.get_document_info(document_id)
        
        if not doc_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Document {document_id} not found"
            )
        
        # Queue for reprocessing
        logger.info(f"Document {document_id} queued for reprocessing")
        
        return {
            "status": "processing",
            "message": f"Document {document_id} queued for reprocessing"
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Document reprocess error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reprocess document"
        )