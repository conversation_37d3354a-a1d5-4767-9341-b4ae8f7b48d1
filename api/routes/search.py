"""
Search endpoints for RAG functionality.

This module provides search capabilities across the document store
with various search strategies.
"""

from typing import List, Dict, Any
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status

from ..models import SearchRequest, SearchResponse, SearchResultItem
from ..app import get_vector_store, get_logger
from ...rag.search import create_search_strategy
from ...providers import get_embedding_provider
from ...utils import validate_query

router = APIRouter()
logger = get_logger(__name__)


@router.post("/search", response_model=SearchResponse)
async def search_documents(
    request: SearchRequest,
    vector_store=Depends(get_vector_store)
):
    """
    Search documents using specified strategy.
    
    Supports vector, keyword, and hybrid search strategies.
    """
    # Validate query
    if not validate_query(request.query):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid search query"
        )
    
    try:
        start_time = datetime.now()
        
        # Create search strategy
        search_strategy = create_search_strategy(
            search_type=request.search_type,
            vector_store=vector_store,
            embedding_provider=await get_embedding_provider()
        )
        
        # Perform search
        results = await search_strategy.search(
            query=request.query,
            limit=request.limit,
            filters=request.filters
        )
        
        # Calculate execution time
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # Format results
        result_items = []
        for result in results:
            item = SearchResultItem(
                chunk_id=result["chunk_id"],
                document_id=result["document_id"],
                content=result["content"],
                score=result.get("score") if request.include_scores else None,
                metadata=result.get("metadata", {}),
                highlights=result.get("highlights")
            )
            result_items.append(item)
        
        return SearchResponse(
            query=request.query,
            results=result_items,
            total_results=len(result_items),
            search_type=request.search_type,
            execution_time=execution_time
        )
    
    except Exception as e:
        logger.error(f"Search error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform search"
        )


@router.post("/search/similar/{chunk_id}")
async def find_similar_chunks(
    chunk_id: str,
    limit: int = 10,
    vector_store=Depends(get_vector_store)
):
    """
    Find chunks similar to a given chunk.
    
    Args:
        chunk_id: Reference chunk ID
        limit: Maximum number of results
    """
    try:
        # Get the chunk
        chunk = await vector_store.get_chunk(chunk_id)
        
        if not chunk:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Chunk {chunk_id} not found"
            )
        
        # Get chunk embedding
        embedding = chunk.get("embedding")
        if not embedding:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Chunk has no embedding"
            )
        
        # Search for similar chunks
        results = await vector_store.search_by_vector(
            embedding=embedding,
            limit=limit + 1  # Include the source chunk
        )
        
        # Filter out the source chunk
        similar_chunks = [
            {
                "chunk_id": r["chunk_id"],
                "document_id": r["document_id"],
                "content": r["content"],
                "score": r["score"],
                "metadata": r.get("metadata", {})
            }
            for r in results
            if r["chunk_id"] != chunk_id
        ][:limit]
        
        return {
            "source_chunk_id": chunk_id,
            "similar_chunks": similar_chunks,
            "count": len(similar_chunks)
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Similar search error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to find similar chunks"
        )


@router.get("/search/suggest")
async def search_suggestions(
    q: str,
    limit: int = 5,
    vector_store=Depends(get_vector_store)
):
    """
    Get search suggestions based on partial query.
    
    Args:
        q: Partial query string
        limit: Maximum number of suggestions
    """
    try:
        # Validate input
        if not q or len(q) < 2:
            return {"suggestions": []}
        
        # Get suggestions from vector store
        # This is a simplified implementation
        # You might want to use a dedicated search index for this
        
        suggestions = await vector_store.get_search_suggestions(
            prefix=q,
            limit=limit
        )
        
        return {
            "query": q,
            "suggestions": suggestions
        }
    
    except Exception as e:
        logger.error(f"Suggestion error: {e}")
        # Don't fail hard on suggestion errors
        return {"query": q, "suggestions": []}


@router.post("/search/batch")
async def batch_search(
    queries: List[str],
    search_type: str = "hybrid",
    limit: int = 10,
    vector_store=Depends(get_vector_store)
):
    """
    Perform batch search for multiple queries.
    
    Args:
        queries: List of search queries
        search_type: Search strategy to use
        limit: Maximum results per query
    """
    if len(queries) > 10:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Maximum 10 queries allowed per batch"
        )
    
    try:
        results = {}
        
        # Create search strategy
        search_strategy = create_search_strategy(
            search_type=search_type,
            vector_store=vector_store,
            embedding_provider=await get_embedding_provider()
        )
        
        # Process each query
        for query in queries:
            if validate_query(query):
                try:
                    query_results = await search_strategy.search(
                        query=query,
                        limit=limit
                    )
                    
                    results[query] = {
                        "status": "success",
                        "results": query_results,
                        "count": len(query_results)
                    }
                except Exception as e:
                    logger.error(f"Batch search error for query '{query}': {e}")
                    results[query] = {
                        "status": "error",
                        "error": str(e)
                    }
            else:
                results[query] = {
                    "status": "error",
                    "error": "Invalid query"
                }
        
        return {
            "batch_results": results,
            "total_queries": len(queries),
            "successful_queries": sum(1 for r in results.values() if r["status"] == "success")
        }
    
    except Exception as e:
        logger.error(f"Batch search error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform batch search"
        )


@router.get("/search/stats")
async def get_search_stats(
    vector_store=Depends(get_vector_store)
):
    """
    Get search statistics and metadata.
    
    Returns information about the search index and capabilities.
    """
    try:
        # Get vector store statistics
        stats = await vector_store.get_statistics()
        
        return {
            "total_documents": stats.get("document_count", 0),
            "total_chunks": stats.get("chunk_count", 0),
            "index_size": stats.get("index_size", 0),
            "embedding_dimension": stats.get("embedding_dimension", 1536),
            "supported_search_types": ["vector", "keyword", "hybrid"],
            "max_results_per_query": 100,
            "features": {
                "metadata_filtering": True,
                "similarity_threshold": True,
                "batch_search": True,
                "search_suggestions": True
            }
        }
    
    except Exception as e:
        logger.error(f"Stats error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get search statistics"
        )