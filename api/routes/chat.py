"""
Chat endpoints for agent interactions.

This module handles chat-based interactions with the RAG agent,
including streaming and non-streaming responses.
"""

import asyncio
import uuid
from typing import AsyncGenerator
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse

from ..models import ChatRequest, ChatResponse, StreamChunk, ErrorResponse
from ..app import get_agent, get_logger
from ...utils import validate_query, RateLimiter

router = APIRouter()
logger = get_logger(__name__)

# Rate limiter for chat endpoints
chat_rate_limiter = RateLimiter(max_requests=60, window_seconds=60)


def check_rate_limit(user_id: str = None):
    """Check rate limit for user."""
    key = user_id or "anonymous"
    if not chat_rate_limiter.is_allowed(key):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded"
        )


@router.post("/chat", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    agent=Depends(get_agent)
):
    """
    Send a message to the agent and receive a response.
    
    This endpoint supports both streaming and non-streaming responses.
    For streaming, set `stream=true` in the request.
    """
    # Rate limiting
    check_rate_limit(request.user_id)
    
    # Validate input
    if not validate_query(request.message):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid message content"
        )
    
    try:
        if request.stream:
            # Return streaming response
            return StreamingResponse(
                stream_chat(request, agent),
                media_type="text/event-stream"
            )
        else:
            # Non-streaming response
            result = await agent.query(
                message=request.message,
                session_id=request.session_id,
                metadata=request.metadata
            )
            
            return ChatResponse(
                session_id=request.session_id,
                message=result["answer"],
                timestamp=datetime.now(),
                metadata=result.get("metadata", {}),
                usage=result.get("usage")
            )
    
    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process chat request"
        )


async def stream_chat(
    request: ChatRequest,
    agent
) -> AsyncGenerator[str, None]:
    """
    Stream chat responses using Server-Sent Events.
    
    Args:
        request: Chat request
        agent: Agent instance
    
    Yields:
        SSE formatted chunks
    """
    try:
        # Start streaming
        async for chunk in agent.stream_query(
            message=request.message,
            session_id=request.session_id,
            metadata=request.metadata
        ):
            # Format as SSE
            chunk_data = StreamChunk(
                session_id=request.session_id,
                chunk=chunk.get("chunk", ""),
                is_final=chunk.get("is_final", False),
                metadata=chunk.get("metadata")
            )
            
            yield f"data: {chunk_data.model_dump_json()}\n\n"
            
            # Small delay to prevent overwhelming client
            await asyncio.sleep(0.01)
    
    except Exception as e:
        logger.error(f"Streaming error: {e}")
        error_data = ErrorResponse(
            error="streaming_error",
            message=str(e),
            timestamp=datetime.now()
        )
        yield f"data: {error_data.model_dump_json()}\n\n"
    
    finally:
        # Send final close event
        yield "event: close\ndata: {}\n\n"


@router.post("/chat/feedback")
async def chat_feedback(
    session_id: str,
    helpful: bool,
    feedback: str = None,
    agent=Depends(get_agent)
):
    """
    Submit feedback for a chat session.
    
    Args:
        session_id: Chat session ID
        helpful: Whether the response was helpful
        feedback: Optional text feedback
    """
    try:
        # Store feedback (implement based on your needs)
        logger.info(
            f"Feedback received for session {session_id}",
            extra={
                "session_id": session_id,
                "helpful": helpful,
                "feedback": feedback
            }
        )
        
        return {"status": "success", "message": "Feedback recorded"}
    
    except Exception as e:
        logger.error(f"Feedback error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to record feedback"
        )


@router.get("/chat/history/{session_id}")
async def get_chat_history(
    session_id: str,
    limit: int = 50,
    agent=Depends(get_agent)
):
    """
    Get chat history for a session.
    
    Args:
        session_id: Session ID
        limit: Maximum number of messages to return
    """
    try:
        # Get conversation history
        history = await agent.get_conversation_history(
            session_id=session_id,
            limit=limit
        )
        
        return {
            "session_id": session_id,
            "messages": history,
            "count": len(history)
        }
    
    except Exception as e:
        logger.error(f"History retrieval error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve chat history"
        )


@router.delete("/chat/history/{session_id}")
async def clear_chat_history(
    session_id: str,
    agent=Depends(get_agent)
):
    """
    Clear chat history for a session.
    
    Args:
        session_id: Session ID to clear
    """
    try:
        # Clear conversation history
        await agent.clear_conversation_history(session_id=session_id)
        
        return {
            "status": "success",
            "message": f"History cleared for session {session_id}"
        }
    
    except Exception as e:
        logger.error(f"History clear error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clear chat history"
        )