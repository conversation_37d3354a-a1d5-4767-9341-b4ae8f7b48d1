"""
API request and response models.

This module defines Pydantic models for API endpoints,
ensuring type safety and automatic validation.
"""

from typing import List, Dict, Any, Optional, Literal
from datetime import datetime
from pydantic import BaseModel, Field, validator
import uuid


# Request Models

class ChatRequest(BaseModel):
    """Chat request model."""
    message: str = Field(..., description="User message", min_length=1, max_length=10000)
    session_id: Optional[str] = Field(None, description="Session ID for conversation tracking")
    user_id: Optional[str] = Field(None, description="User identifier")
    stream: bool = Field(False, description="Enable streaming response")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    
    @validator('session_id', pre=True, always=True)
    def generate_session_id(cls, v):
        """Generate session ID if not provided."""
        return v or str(uuid.uuid4())


class SearchRequest(BaseModel):
    """Search request model."""
    query: str = Field(..., description="Search query", min_length=1, max_length=500)
    search_type: Literal["vector", "keyword", "hybrid"] = Field("hybrid", description="Search strategy")
    limit: int = Field(10, description="Maximum results", ge=1, le=100)
    filters: Optional[Dict[str, Any]] = Field(None, description="Metadata filters")
    include_scores: bool = Field(True, description="Include relevance scores")


class DocumentUploadRequest(BaseModel):
    """Document upload request model."""
    title: str = Field(..., description="Document title", min_length=1, max_length=200)
    source: str = Field(..., description="Document source/URL")
    content: str = Field(..., description="Document content", min_length=1)
    metadata: Optional[Dict[str, Any]] = Field(None, description="Document metadata")
    process_immediately: bool = Field(True, description="Process document immediately")


class DocumentQueryRequest(BaseModel):
    """Document query request model."""
    document_id: str = Field(..., description="Document ID")
    include_chunks: bool = Field(False, description="Include document chunks")
    include_metadata: bool = Field(True, description="Include metadata")


# Response Models

class ChatResponse(BaseModel):
    """Chat response model."""
    session_id: str = Field(..., description="Session ID")
    message: str = Field(..., description="Agent response")
    timestamp: datetime = Field(..., description="Response timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Response metadata")
    usage: Optional[Dict[str, int]] = Field(None, description="Token usage statistics")


class StreamChunk(BaseModel):
    """Streaming response chunk."""
    session_id: str = Field(..., description="Session ID")
    chunk: str = Field(..., description="Response chunk")
    is_final: bool = Field(False, description="Whether this is the final chunk")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Chunk metadata")


class SearchResultItem(BaseModel):
    """Individual search result."""
    chunk_id: str = Field(..., description="Chunk ID")
    document_id: str = Field(..., description="Document ID")
    content: str = Field(..., description="Chunk content")
    score: Optional[float] = Field(None, description="Relevance score")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Result metadata")
    highlights: Optional[List[str]] = Field(None, description="Highlighted snippets")


class SearchResponse(BaseModel):
    """Search response model."""
    query: str = Field(..., description="Original query")
    results: List[SearchResultItem] = Field(..., description="Search results")
    total_results: int = Field(..., description="Total number of results")
    search_type: str = Field(..., description="Search type used")
    execution_time: float = Field(..., description="Search execution time")


class DocumentInfo(BaseModel):
    """Document information model."""
    document_id: str = Field(..., description="Document ID")
    title: str = Field(..., description="Document title")
    source: str = Field(..., description="Document source")
    chunk_count: int = Field(..., description="Number of chunks")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Update timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Document metadata")


class DocumentUploadResponse(BaseModel):
    """Document upload response model."""
    document_id: str = Field(..., description="Generated document ID")
    status: Literal["processing", "completed", "failed"] = Field(..., description="Processing status")
    message: str = Field(..., description="Status message")
    chunk_count: Optional[int] = Field(None, description="Number of chunks created")
    processing_time: Optional[float] = Field(None, description="Processing time in seconds")


class DocumentListResponse(BaseModel):
    """Document list response model."""
    documents: List[DocumentInfo] = Field(..., description="List of documents")
    total_count: int = Field(..., description="Total number of documents")
    page: int = Field(..., description="Current page")
    per_page: int = Field(..., description="Items per page")


# Status Models

class HealthResponse(BaseModel):
    """Health check response model."""
    status: Literal["healthy", "degraded", "unhealthy"] = Field(..., description="Health status")
    version: str = Field(..., description="API version")
    timestamp: datetime = Field(..., description="Check timestamp")
    services: Dict[str, str] = Field(..., description="Service statuses")


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    request_id: Optional[str] = Field(None, description="Request ID for tracking")
    timestamp: datetime = Field(default_factory=datetime.now, description="Error timestamp")


class ToolUsageStats(BaseModel):
    """Tool usage statistics."""
    tool_name: str = Field(..., description="Tool name")
    total_calls: int = Field(..., description="Total number of calls")
    successful_calls: int = Field(..., description="Number of successful calls")
    failed_calls: int = Field(..., description="Number of failed calls")
    avg_execution_time: float = Field(..., description="Average execution time")


class SessionInfo(BaseModel):
    """Session information model."""
    session_id: str = Field(..., description="Session ID")
    message_count: int = Field(..., description="Number of messages")
    created_at: datetime = Field(..., description="Session creation time")
    last_activity: datetime = Field(..., description="Last activity time")
    tool_usage: List[ToolUsageStats] = Field(default_factory=list, description="Tool usage statistics")


# Configuration Models

class AgentConfigUpdate(BaseModel):
    """Agent configuration update model."""
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0, description="Model temperature")
    max_tokens: Optional[int] = Field(None, ge=1, description="Maximum response tokens")
    search_limit: Optional[int] = Field(None, ge=1, le=50, description="Default search limit")
    enable_streaming: Optional[bool] = Field(None, description="Enable streaming responses")
    system_prompt: Optional[str] = Field(None, description="Custom system prompt")


class ModelInfo(BaseModel):
    """Model information."""
    provider: str = Field(..., description="Provider name")
    model: str = Field(..., description="Model name")
    capabilities: List[str] = Field(..., description="Model capabilities")
    context_window: int = Field(..., description="Context window size")
    max_output_tokens: Optional[int] = Field(None, description="Maximum output tokens")


class SystemInfoResponse(BaseModel):
    """System information response."""
    agent_name: str = Field(..., description="Agent name")
    agent_version: str = Field(..., description="Agent version")
    available_tools: List[str] = Field(..., description="Available tools")
    llm_model: ModelInfo = Field(..., description="LLM model info")
    embedding_model: ModelInfo = Field(..., description="Embedding model info")
    storage_backend: str = Field(..., description="Storage backend type")
    document_count: int = Field(..., description="Total documents")
    chunk_count: int = Field(..., description="Total chunks")