"""
FastAPI application for the RAG Agent.

This module sets up the main FastAPI application with:
- CORS configuration
- Exception handlers
- Request/response logging
- Health checks
- API versioning
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator
import os
import time
import logging
from datetime import datetime

from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from ..utils import setup_logging, get_logger, with_correlation_id
from ..providers import get_llm_provider, get_embedding_provider
from ..rag.storage import create_vector_store
from ..agent import RAGAgent, AgentConfig, ToolRegistry
from .models import ErrorR<PERSON>ponse, HealthResponse
from .routes import chat, documents, search, system

# Setup logging
setup_logging()
logger = get_logger(__name__)

# Global instances
agent_instance = None
vector_store_instance = None
tool_registry_instance = None


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator:
    """
    Application lifespan manager.
    
    Handles startup and shutdown tasks.
    """
    # Startup
    logger.info("Starting RAG Agent API...")
    
    global agent_instance, vector_store_instance, tool_registry_instance
    
    try:
        # Initialize providers
        llm_provider = get_llm_provider()
        embedding_provider = get_embedding_provider()
        
        # Initialize vector store
        vector_store_instance = await create_vector_store(
            embedding_provider=embedding_provider
        )
        await vector_store_instance.initialize()
        
        # Initialize tool registry
        tool_registry_instance = ToolRegistry()
        tool_registry_instance.setup_default_tools(vector_store_instance)
        
        # Initialize agent
        config = AgentConfig(
            name=os.getenv("AGENT_NAME", "RAG Assistant")
        )
        agent_instance = RAGAgent(
            config=config,
            llm_provider=llm_provider,
            tool_registry=tool_registry_instance
        )
        
        logger.info("RAG Agent API started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down RAG Agent API...")
    
    # Cleanup connections
    if vector_store_instance:
        await vector_store_instance.close()
    
    logger.info("RAG Agent API stopped")


# Create FastAPI app
app = FastAPI(
    title="RAG Agent API",
    description="Production-ready AI agent with RAG capabilities",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("CORS_ORIGINS", "*").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Request ID middleware
@app.middleware("http")
async def add_request_id(request: Request, call_next):
    """Add correlation ID to requests."""
    request_id = request.headers.get("X-Request-ID", None)
    
    with with_correlation_id(request_id):
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time
        
        response.headers["X-Process-Time"] = str(process_time)
        response.headers["X-Request-ID"] = get_logger().extra.get("correlation_id", "")
        
        return response


# Request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log incoming requests and responses."""
    # Log request
    logger.info(
        f"Request: {request.method} {request.url.path}",
        extra={
            "method": request.method,
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "client": request.client.host if request.client else None,
        }
    )
    
    # Process request
    response = await call_next(request)
    
    # Log response
    logger.info(
        f"Response: {response.status_code}",
        extra={
            "status_code": response.status_code,
            "process_time": response.headers.get("X-Process-Time"),
        }
    )
    
    return response


# Exception handlers
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle validation errors."""
    logger.warning(f"Validation error: {exc}")
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=ErrorResponse(
            error="validation_error",
            message="Invalid request data",
            details={"errors": exc.errors()},
            request_id=request.headers.get("X-Request-ID")
        ).model_dump()
    )


@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """Handle HTTP exceptions."""
    logger.error(f"HTTP exception: {exc}")
    
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error="http_error",
            message=exc.detail,
            request_id=request.headers.get("X-Request-ID")
        ).model_dump()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions."""
    logger.exception(f"Unhandled exception: {exc}")
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(
            error="internal_error",
            message="An internal error occurred",
            request_id=request.headers.get("X-Request-ID")
        ).model_dump()
    )


# Health check endpoint
@app.get("/health", response_model=HealthResponse, tags=["System"])
async def health_check():
    """
    Health check endpoint.
    
    Returns the current health status of the application.
    """
    services = {}
    overall_status = "healthy"
    
    # Check agent
    if agent_instance:
        services["agent"] = "healthy"
    else:
        services["agent"] = "unhealthy"
        overall_status = "unhealthy"
    
    # Check vector store
    if vector_store_instance:
        try:
            # Simple connectivity check
            await vector_store_instance.search_by_vector(
                embedding=[0.0] * 1536,
                limit=1
            )
            services["vector_store"] = "healthy"
        except Exception as e:
            logger.error(f"Vector store health check failed: {e}")
            services["vector_store"] = "unhealthy"
            overall_status = "degraded" if overall_status == "healthy" else overall_status
    else:
        services["vector_store"] = "unhealthy"
        overall_status = "unhealthy"
    
    # Check LLM provider
    try:
        provider = get_llm_provider()
        services["llm_provider"] = "healthy"
    except Exception as e:
        logger.error(f"LLM provider health check failed: {e}")
        services["llm_provider"] = "unhealthy"
        overall_status = "degraded" if overall_status == "healthy" else overall_status
    
    return HealthResponse(
        status=overall_status,
        version=app.version,
        timestamp=datetime.now(),
        services=services
    )


# Include routers
app.include_router(
    chat.router,
    prefix="/api/v1",
    tags=["Chat"]
)

app.include_router(
    documents.router,
    prefix="/api/v1",
    tags=["Documents"]
)

app.include_router(
    search.router,
    prefix="/api/v1",
    tags=["Search"]
)

app.include_router(
    system.router,
    prefix="/api/v1",
    tags=["System"]
)


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "name": "RAG Agent API",
        "version": app.version,
        "docs": "/api/docs",
        "health": "/health"
    }


def get_agent() -> RAGAgent:
    """Get the agent instance."""
    if not agent_instance:
        raise RuntimeError("Agent not initialized")
    return agent_instance


def get_vector_store():
    """Get the vector store instance."""
    if not vector_store_instance:
        raise RuntimeError("Vector store not initialized")
    return vector_store_instance


def get_tool_registry() -> ToolRegistry:
    """Get the tool registry instance."""
    if not tool_registry_instance:
        raise RuntimeError("Tool registry not initialized")
    return tool_registry_instance


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "src.api.app:app",
        host=os.getenv("API_HOST", "0.0.0.0"),
        port=int(os.getenv("API_PORT", "8000")),
        reload=os.getenv("API_RELOAD", "false").lower() == "true",
        log_level=os.getenv("LOG_LEVEL", "info").lower()
    )