"""
FastAPI application for the RAG Agent.

This package provides a REST API interface for interacting with
the RAG agent, including chat, document management, and search endpoints.
"""

from .app import app, get_agent, get_vector_store, get_tool_registry
from .models import (
    # Request models
    ChatRequest,
    SearchRequest,
    DocumentUploadRequest,
    DocumentQueryRequest,
    AgentConfigUpdate,
    
    # Response models
    ChatResponse,
    StreamChunk,
    SearchResponse,
    SearchResultItem,
    DocumentInfo,
    DocumentUploadResponse,
    DocumentListResponse,
    HealthResponse,
    ErrorResponse,
    SystemInfoResponse,
    ModelInfo,
    SessionInfo,
    ToolUsageStats,
)

__all__ = [
    # App and dependencies
    "app",
    "get_agent",
    "get_vector_store",
    "get_tool_registry",
    
    # Request models
    "ChatRequest",
    "SearchRequest",
    "DocumentUploadRequest",
    "DocumentQueryRequest",
    "AgentConfigUpdate",
    
    # Response models
    "ChatResponse",
    "StreamChunk",
    "SearchResponse",
    "SearchResultItem",
    "DocumentInfo",
    "DocumentUploadResponse",
    "DocumentListResponse",
    "HealthResponse",
    "ErrorResponse",
    "SystemInfoResponse",
    "ModelInfo",
    "SessionInfo",
    "ToolUsageStats",
]