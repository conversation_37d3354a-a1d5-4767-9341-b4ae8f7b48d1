"""
Logging utilities for structured logging.

This module provides enhanced logging capabilities with structured output,
correlation IDs, and performance tracking.
"""

import logging
import json
import time
import uuid
from datetime import datetime
from typing import Any, Dict, Optional, Callable
from functools import wraps
from contextvars import ContextVar

# Context variable for request/session correlation
correlation_id: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)


class StructuredFormatter(logging.Formatter):
    """JSON formatter for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
        # Base log data
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add correlation ID if available
        corr_id = correlation_id.get()
        if corr_id:
            log_data["correlation_id"] = corr_id
        
        # Add exception info if present
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'created', 'filename', 'funcName',
                          'levelname', 'levelno', 'lineno', 'module', 'exc_info',
                          'exc_text', 'stack_info', 'pathname', 'processName',
                          'process', 'threadName', 'thread', 'getMessage', 'message']:
                log_data[key] = value
        
        return json.dumps(log_data)


class PerformanceFilter(logging.Filter):
    """Filter that adds performance metrics to log records."""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Add performance data to record."""
        # Add memory usage
        try:
            import psutil
            process = psutil.Process()
            record.memory_mb = process.memory_info().rss / 1024 / 1024
            record.cpu_percent = process.cpu_percent()
        except:
            pass
        
        return True


def setup_logging(
    level: str = "INFO",
    format_type: str = "json",
    log_file: Optional[str] = None
) -> None:
    """
    Setup logging configuration.
    
    Args:
        level: Log level
        format_type: Format type (json or text)
        log_file: Optional log file path
    """
    # Get root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))
    
    # Remove existing handlers
    root_logger.handlers.clear()
    
    # Create console handler
    console_handler = logging.StreamHandler()
    
    # Set formatter
    if format_type == "json":
        formatter = StructuredFormatter()
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    console_handler.setFormatter(formatter)
    
    # Add performance filter
    console_handler.addFilter(PerformanceFilter())
    
    # Add handler to logger
    root_logger.addHandler(console_handler)
    
    # Add file handler if specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        file_handler.addFilter(PerformanceFilter())
        root_logger.addHandler(file_handler)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the given name.
    
    Args:
        name: Logger name
    
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


def with_correlation_id(func: Callable) -> Callable:
    """
    Decorator to add correlation ID to function execution.
    
    Args:
        func: Function to wrap
    
    Returns:
        Wrapped function
    """
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        # Generate correlation ID if not set
        if correlation_id.get() is None:
            correlation_id.set(str(uuid.uuid4()))
        
        return await func(*args, **kwargs)
    
    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        # Generate correlation ID if not set
        if correlation_id.get() is None:
            correlation_id.set(str(uuid.uuid4()))
        
        return func(*args, **kwargs)
    
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper


def log_execution_time(logger: Optional[logging.Logger] = None):
    """
    Decorator to log function execution time.
    
    Args:
        logger: Logger to use (defaults to function's module logger)
    
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        nonlocal logger
        if logger is None:
            logger = logging.getLogger(func.__module__)
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.info(
                    f"{func.__name__} completed",
                    extra={
                        "function": func.__name__,
                        "execution_time": execution_time,
                        "status": "success"
                    }
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"{func.__name__} failed",
                    extra={
                        "function": func.__name__,
                        "execution_time": execution_time,
                        "status": "error",
                        "error": str(e)
                    },
                    exc_info=True
                )
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.info(
                    f"{func.__name__} completed",
                    extra={
                        "function": func.__name__,
                        "execution_time": execution_time,
                        "status": "success"
                    }
                )
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(
                    f"{func.__name__} failed",
                    extra={
                        "function": func.__name__,
                        "execution_time": execution_time,
                        "status": "error",
                        "error": str(e)
                    },
                    exc_info=True
                )
                raise
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class LogContext:
    """Context manager for adding context to logs."""
    
    def __init__(self, **kwargs):
        self.context = kwargs
        self.token = None
    
    def __enter__(self):
        """Enter context."""
        # Set correlation ID if provided
        if 'correlation_id' in self.context:
            self.token = correlation_id.set(self.context['correlation_id'])
        
        # Add context to thread local storage for loggers
        for key, value in self.context.items():
            if key != 'correlation_id':
                # This would need custom logger adapter implementation
                pass
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context."""
        if self.token:
            correlation_id.reset(self.token)


# Convenience functions for common log patterns

def log_api_request(
    logger: logging.Logger,
    method: str,
    path: str,
    params: Optional[Dict[str, Any]] = None,
    body: Optional[Any] = None
) -> None:
    """Log API request details."""
    logger.info(
        f"API Request: {method} {path}",
        extra={
            "http_method": method,
            "path": path,
            "params": params,
            "body_size": len(str(body)) if body else 0,
            "event_type": "api_request"
        }
    )


def log_api_response(
    logger: logging.Logger,
    status_code: int,
    response_time: float,
    response_size: Optional[int] = None
) -> None:
    """Log API response details."""
    logger.info(
        f"API Response: {status_code}",
        extra={
            "status_code": status_code,
            "response_time": response_time,
            "response_size": response_size,
            "event_type": "api_response"
        }
    )


def log_database_query(
    logger: logging.Logger,
    query: str,
    params: Optional[Dict[str, Any]] = None,
    execution_time: Optional[float] = None
) -> None:
    """Log database query details."""
    logger.debug(
        "Database query executed",
        extra={
            "query": query[:200],  # Truncate long queries
            "params_count": len(params) if params else 0,
            "execution_time": execution_time,
            "event_type": "db_query"
        }
    )


import asyncio