"""
Input validation and sanitization utilities.

This module provides functions for validating and sanitizing user inputs
to ensure security and data integrity.
"""

import re
import html
import logging
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse
import validators
from pydantic import BaseModel, ValidationError

logger = logging.getLogger(__name__)


# Common patterns
EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
PHONE_PATTERN = re.compile(r'^\+?1?\d{9,15}$')
ALPHANUMERIC_PATTERN = re.compile(r'^[a-zA-Z0-9]+$')
SQL_INJECTION_PATTERN = re.compile(
    r'(\b(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript)\b)',
    re.IGNORECASE
)
XSS_PATTERN = re.compile(r'<script|javascript:|onerror=|onload=|onclick=', re.IGNORECASE)


def sanitize_input(text: str, max_length: Optional[int] = None) -> str:
    """
    Sanitize text input by removing potentially harmful content.
    
    Args:
        text: Input text
        max_length: Optional maximum length
    
    Returns:
        Sanitized text
    """
    if not text:
        return ""
    
    # Strip leading/trailing whitespace
    text = text.strip()
    
    # Limit length if specified
    if max_length and len(text) > max_length:
        text = text[:max_length]
    
    # Escape HTML entities
    text = html.escape(text)
    
    # Remove null bytes
    text = text.replace('\x00', '')
    
    # Remove control characters
    text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\r\t')
    
    return text


def validate_email(email: str) -> bool:
    """
    Validate email address format.
    
    Args:
        email: Email address
    
    Returns:
        True if valid, False otherwise
    """
    if not email or not isinstance(email, str):
        return False
    
    email = email.strip().lower()
    return bool(EMAIL_PATTERN.match(email))


def validate_url(url: str, require_https: bool = False) -> bool:
    """
    Validate URL format and optionally require HTTPS.
    
    Args:
        url: URL to validate
        require_https: Whether to require HTTPS
    
    Returns:
        True if valid, False otherwise
    """
    if not url or not isinstance(url, str):
        return False
    
    # Use validators library
    if not validators.url(url):
        return False
    
    # Check scheme if required
    if require_https:
        parsed = urlparse(url)
        return parsed.scheme == 'https'
    
    return True


def validate_phone(phone: str) -> bool:
    """
    Validate phone number format.
    
    Args:
        phone: Phone number
    
    Returns:
        True if valid, False otherwise
    """
    if not phone or not isinstance(phone, str):
        return False
    
    # Remove common separators
    phone = re.sub(r'[\s\-\(\)]+', '', phone)
    
    return bool(PHONE_PATTERN.match(phone))


def validate_alphanumeric(text: str) -> bool:
    """
    Validate that text contains only alphanumeric characters.
    
    Args:
        text: Text to validate
    
    Returns:
        True if valid, False otherwise
    """
    if not text or not isinstance(text, str):
        return False
    
    return bool(ALPHANUMERIC_PATTERN.match(text))


def check_sql_injection(text: str) -> bool:
    """
    Check for potential SQL injection patterns.
    
    Args:
        text: Text to check
    
    Returns:
        True if suspicious patterns found, False otherwise
    """
    if not text:
        return False
    
    return bool(SQL_INJECTION_PATTERN.search(text))


def check_xss(text: str) -> bool:
    """
    Check for potential XSS patterns.
    
    Args:
        text: Text to check
    
    Returns:
        True if suspicious patterns found, False otherwise
    """
    if not text:
        return False
    
    return bool(XSS_PATTERN.search(text))


def validate_query(query: str, max_length: int = 500) -> bool:
    """
    Validate a search query.
    
    Args:
        query: Search query
        max_length: Maximum allowed length
    
    Returns:
        True if valid, False otherwise
    """
    if not query or not isinstance(query, str):
        return False
    
    query = query.strip()
    
    # Check length
    if len(query) == 0 or len(query) > max_length:
        return False
    
    # Check for injection attempts
    if check_sql_injection(query) or check_xss(query):
        logger.warning(f"Suspicious query detected: {query[:50]}...")
        return False
    
    return True


def validate_file_name(filename: str, allowed_extensions: Optional[List[str]] = None) -> bool:
    """
    Validate file name for security.
    
    Args:
        filename: File name to validate
        allowed_extensions: Optional list of allowed extensions
    
    Returns:
        True if valid, False otherwise
    """
    if not filename or not isinstance(filename, str):
        return False
    
    # Check for path traversal
    if '..' in filename or '/' in filename or '\\' in filename:
        return False
    
    # Check for null bytes
    if '\x00' in filename:
        return False
    
    # Check extension if specified
    if allowed_extensions:
        ext = filename.lower().split('.')[-1] if '.' in filename else ''
        if ext not in allowed_extensions:
            return False
    
    return True


def sanitize_dict(data: Dict[str, Any], max_depth: int = 10) -> Dict[str, Any]:
    """
    Recursively sanitize dictionary values.
    
    Args:
        data: Dictionary to sanitize
        max_depth: Maximum recursion depth
    
    Returns:
        Sanitized dictionary
    """
    if max_depth <= 0:
        return {}
    
    sanitized = {}
    
    for key, value in data.items():
        # Sanitize key
        if isinstance(key, str):
            key = sanitize_input(key, max_length=100)
        
        # Sanitize value based on type
        if isinstance(value, str):
            sanitized[key] = sanitize_input(value)
        elif isinstance(value, dict):
            sanitized[key] = sanitize_dict(value, max_depth - 1)
        elif isinstance(value, list):
            sanitized[key] = [
                sanitize_dict(item, max_depth - 1) if isinstance(item, dict)
                else sanitize_input(item) if isinstance(item, str)
                else item
                for item in value
            ]
        else:
            sanitized[key] = value
    
    return sanitized


def validate_json_schema(data: Any, schema: type[BaseModel]) -> Union[BaseModel, None]:
    """
    Validate data against a Pydantic schema.
    
    Args:
        data: Data to validate
        schema: Pydantic model class
    
    Returns:
        Validated model instance or None if validation fails
    """
    try:
        return schema(**data)
    except ValidationError as e:
        logger.error(f"Schema validation failed: {e}")
        return None


def sanitize_markdown(text: str) -> str:
    """
    Sanitize markdown text while preserving formatting.
    
    Args:
        text: Markdown text
    
    Returns:
        Sanitized markdown
    """
    if not text:
        return ""
    
    # Remove script tags but preserve other markdown
    text = re.sub(r'<script[^>]*>.*?</script>', '', text, flags=re.DOTALL | re.IGNORECASE)
    
    # Remove javascript: links
    text = re.sub(r'\[([^\]]+)\]\(javascript:[^\)]+\)', r'\1', text)
    
    # Remove event handlers
    text = re.sub(r'\son\w+\s*=\s*["\'][^"\']*["\']', '', text)
    
    return text


def validate_pagination(
    page: int = 1,
    per_page: int = 20,
    max_per_page: int = 100
) -> tuple[int, int]:
    """
    Validate and sanitize pagination parameters.
    
    Args:
        page: Page number
        per_page: Items per page
        max_per_page: Maximum allowed items per page
    
    Returns:
        Tuple of (offset, limit)
    """
    # Ensure positive integers
    page = max(1, int(page))
    per_page = max(1, min(int(per_page), max_per_page))
    
    # Calculate offset
    offset = (page - 1) * per_page
    
    return offset, per_page


# Rate limiting helpers

class RateLimiter:
    """Simple in-memory rate limiter."""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests: Dict[str, List[float]] = {}
    
    def is_allowed(self, key: str) -> bool:
        """
        Check if request is allowed for the given key.
        
        Args:
            key: Rate limit key (e.g., IP address, user ID)
        
        Returns:
            True if allowed, False if rate limited
        """
        import time
        now = time.time()
        
        # Clean old requests
        if key in self.requests:
            self.requests[key] = [
                timestamp for timestamp in self.requests[key]
                if now - timestamp < self.window_seconds
            ]
        else:
            self.requests[key] = []
        
        # Check rate limit
        if len(self.requests[key]) >= self.max_requests:
            return False
        
        # Record request
        self.requests[key].append(now)
        return True
    
    def reset(self, key: str):
        """Reset rate limit for a key."""
        if key in self.requests:
            del self.requests[key]