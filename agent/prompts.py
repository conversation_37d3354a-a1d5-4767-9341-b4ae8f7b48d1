"""
Agent prompts and prompt templates.

This module contains system prompts, tool descriptions, and prompt templates
for various agent behaviors and responses.
"""

from typing import Dict, List, Optional
from datetime import datetime
from string import Template


# System Prompts

SYSTEM_PROMPT_BASE = """You are a helpful AI assistant with access to a knowledge base and various tools.

Your capabilities include:
- Searching through documents using semantic similarity and keyword matching
- Retrieving specific documents and their content
- Analyzing and summarizing information
- Extracting entities and relationships
- Providing well-reasoned responses based on available information

Guidelines:
1. Always search for relevant information before providing answers
2. Cite your sources by mentioning document titles or IDs
3. Be transparent about what you know and don't know
4. If information is not in the knowledge base, clearly state this
5. Provide comprehensive yet concise responses
6. Use structured formatting (lists, sections) for clarity when appropriate

Current date and time: ${current_datetime}"""

SYSTEM_PROMPT_RESEARCH = """You are an expert research assistant specializing in comprehensive analysis and synthesis of information.

Your approach to research:
1. **Comprehensive Search**: Always perform multiple searches with different query formulations
2. **Critical Analysis**: Evaluate the credibility and relevance of sources
3. **Synthesis**: Combine information from multiple sources to provide complete answers
4. **Citation**: Always cite specific sources with document names and relevant quotes
5. **Gaps Identification**: Explicitly note what information is missing or unclear
6. **Structured Output**: Organize findings with clear sections and hierarchies

When researching a topic:
- Start with broad searches to understand the landscape
- Follow up with specific searches for details
- Cross-reference information across sources
- Highlight contradictions or disagreements between sources
- Provide confidence levels for your findings

Current date and time: ${current_datetime}"""

SYSTEM_PROMPT_QA = """You are a precise Q&A assistant focused on providing accurate, concise answers from the knowledge base.

Your approach:
1. **Direct Answers**: Provide the most relevant answer immediately
2. **Brevity**: Keep responses concise while maintaining accuracy
3. **Source Attribution**: Always mention which document contains the answer
4. **Confidence**: If unsure, say so clearly
5. **No Speculation**: Only provide information directly supported by the knowledge base

Response format:
- Lead with the direct answer
- Follow with supporting details if necessary
- End with source citation

Current date and time: ${current_datetime}"""

SYSTEM_PROMPT_CREATIVE = """You are a creative AI assistant that uses the knowledge base as inspiration for innovative ideas and solutions.

Your approach:
1. **Knowledge as Foundation**: Use retrieved information as a starting point
2. **Creative Extension**: Build upon existing knowledge with novel connections
3. **Imaginative Solutions**: Propose innovative approaches and ideas
4. **Clear Attribution**: Distinguish between source material and your creative additions
5. **Exploratory Thinking**: Consider multiple perspectives and possibilities

When being creative:
- Acknowledge what comes from sources vs. your generation
- Make unexpected connections between different pieces of information
- Propose "what if" scenarios and thought experiments
- Generate multiple alternative solutions or ideas

Current date and time: ${current_datetime}"""


# Tool Descriptions

TOOL_DESCRIPTIONS = {
    "vector_search": """Search the knowledge base using semantic similarity.
Best for: Finding conceptually related information, exploring topics, discovering relevant content.
Returns: Relevant document chunks ranked by semantic similarity.""",
    
    "keyword_search": """Search the knowledge base using exact keyword matching.
Best for: Finding specific terms, names, or exact phrases.
Returns: Document chunks containing the exact keywords.""",
    
    "hybrid_search": """Search using both semantic similarity and keyword matching.
Best for: Comprehensive searches combining conceptual and exact matching.
Returns: Relevance-ranked results from both search methods.""",
    
    "graph_search": """Search the knowledge graph for entities and relationships.
Best for: Finding connections between concepts, people, or events.
Returns: Entities, relationships, and temporal facts.""",
    
    "get_document": """Retrieve a complete document by its ID.
Best for: Getting full context when you need the entire document.
Returns: Complete document content with metadata.""",
    
    "list_documents": """List available documents in the knowledge base.
Best for: Understanding what information is available, browsing sources.
Returns: Document titles, IDs, and metadata.""",
    
    "summarize": """Generate a summary of provided text.
Best for: Condensing long documents or search results.
Returns: Concise summary preserving key information.""",
    
    "extract_entities": """Extract named entities from text.
Best for: Identifying people, places, organizations, dates, etc.
Returns: Categorized entities with their types.""",
}


# Prompt Templates

CHAIN_OF_THOUGHT_TEMPLATE = """Let me think through this step by step.

${reasoning_steps}

Based on this analysis, here's my response:
${response}"""

REFLECTION_TEMPLATE = """Upon reflection on my response:
- Strengths: ${strengths}
- Potential limitations: ${limitations}
- Confidence level: ${confidence}/10
- Additional considerations: ${considerations}"""

SEARCH_QUERY_EXPANSION_TEMPLATE = """Original query: "${original_query}"

Expanded search queries:
${expanded_queries}

These variations will help find relevant information from different angles."""

NO_RESULTS_TEMPLATE = """I couldn't find specific information about "${query}" in the knowledge base.

What I searched for:
${search_attempts}

This doesn't mean the information doesn't exist, but it's not available in my current knowledge base. Would you like me to:
1. Try searching with different terms?
2. Provide general information on related topics I do have?
3. Help you refine your query?"""

SOURCE_CITATION_TEMPLATE = """Source: ${document_title} (ID: ${document_id})
Relevance: ${relevance_score}
Quote: "${relevant_quote}"
Context: ${context_description}"""

CONFIDENCE_STATEMENT_TEMPLATE = """Confidence Assessment:
- Information completeness: ${completeness}/10
- Source reliability: ${reliability}/10
- Answer certainty: ${certainty}/10
Overall confidence: ${overall}/10

Factors affecting confidence:
${factors}"""


# Prompt Functions

def format_system_prompt(template: str, **kwargs) -> str:
    """
    Format a system prompt template with provided values.
    
    Args:
        template: Prompt template string
        **kwargs: Values to substitute in template
    
    Returns:
        Formatted prompt string
    """
    # Always include current datetime
    kwargs["current_datetime"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Use safe substitute to avoid KeyError on missing values
    return Template(template).safe_substitute(**kwargs)


def create_search_refinement_prompt(
    original_query: str,
    search_results: List[Dict],
    user_feedback: Optional[str] = None
) -> str:
    """
    Create a prompt for refining search based on initial results.
    
    Args:
        original_query: The original search query
        search_results: Initial search results
        user_feedback: Optional user feedback on results
    
    Returns:
        Prompt for search refinement
    """
    prompt_parts = [
        f"Original search query: '{original_query}'",
        f"Found {len(search_results)} results.",
    ]
    
    if search_results:
        prompt_parts.append("\nTop results summary:")
        for i, result in enumerate(search_results[:3], 1):
            prompt_parts.append(f"{i}. {result.get('title', 'Untitled')} - Relevance: {result.get('score', 0):.2f}")
    
    if user_feedback:
        prompt_parts.append(f"\nUser feedback: {user_feedback}")
    
    prompt_parts.extend([
        "\nBased on these results, suggest:",
        "1. Refined search queries to find more relevant information",
        "2. Alternative search strategies",
        "3. Related topics to explore"
    ])
    
    return "\n".join(prompt_parts)


def create_synthesis_prompt(
    query: str,
    search_results: List[Dict],
    require_citations: bool = True
) -> str:
    """
    Create a prompt for synthesizing information from multiple sources.
    
    Args:
        query: The user's query
        search_results: Search results to synthesize
        require_citations: Whether to require source citations
    
    Returns:
        Prompt for information synthesis
    """
    prompt_parts = [
        f"Query: {query}",
        f"\nI found {len(search_results)} relevant sources.",
        "\nPlease synthesize this information to answer the query comprehensively.",
    ]
    
    if require_citations:
        prompt_parts.append("Include specific citations for each piece of information.")
    
    prompt_parts.extend([
        "\nOrganize the response with:",
        "- A direct answer to the query",
        "- Supporting details and context",
        "- Any contradictions or disagreements between sources",
        "- Gaps in the available information"
    ])
    
    return "\n".join(prompt_parts)


def create_tool_selection_prompt(
    task: str,
    available_tools: List[str],
    context: Optional[Dict] = None
) -> str:
    """
    Create a prompt for selecting appropriate tools for a task.
    
    Args:
        task: Description of the task
        available_tools: List of available tool names
        context: Optional context information
    
    Returns:
        Prompt for tool selection
    """
    prompt_parts = [
        f"Task: {task}",
        "\nAvailable tools:"
    ]
    
    for tool in available_tools:
        if tool in TOOL_DESCRIPTIONS:
            prompt_parts.append(f"- {tool}: {TOOL_DESCRIPTIONS[tool]}")
    
    if context:
        prompt_parts.append(f"\nAdditional context: {context}")
    
    prompt_parts.extend([
        "\nSelect the most appropriate tools for this task and explain why.",
        "Consider:",
        "1. Which tools best match the task requirements",
        "2. The optimal order of tool usage",
        "3. How to combine results from multiple tools"
    ])
    
    return "\n".join(prompt_parts)


# Prompt Registry

PROMPT_REGISTRY = {
    "base": SYSTEM_PROMPT_BASE,
    "research": SYSTEM_PROMPT_RESEARCH,
    "qa": SYSTEM_PROMPT_QA,
    "creative": SYSTEM_PROMPT_CREATIVE,
}


def get_system_prompt(prompt_type: str = "base", **kwargs) -> str:
    """
    Get a formatted system prompt by type.
    
    Args:
        prompt_type: Type of prompt (base, research, qa, creative)
        **kwargs: Additional values for template substitution
    
    Returns:
        Formatted system prompt
    """
    template = PROMPT_REGISTRY.get(prompt_type, SYSTEM_PROMPT_BASE)
    return format_system_prompt(template, **kwargs)