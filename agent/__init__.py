"""
Agent module - Core agent implementation with Pydantic AI.

This module provides:
- RAG agent with configurable behavior
- Tool management and execution
- Conversation management
- Prompt templates and system prompts
- Configuration management
"""

from .base import (
    RAGAgent,
    AgentDependencies,
    ConversationManager,
    ToolUsageTracker,
    create_research_agent,
    create_qa_agent,
    create_creative_agent,
)

from .config import (
    AgentConfig,
    ModelConfig,
    PromptConfig,
    BehaviorConfig,
    SearchConfig,
    ToolConfig,
    get_research_agent_config,
    get_qa_agent_config,
    get_creative_agent_config,
)

from .prompts import (
    get_system_prompt,
    format_system_prompt,
    create_search_refinement_prompt,
    create_synthesis_prompt,
    create_tool_selection_prompt,
    PROMPT_REGISTRY,
    TOOL_DESCRIPTIONS,
)

from .tools import (
    SearchInput,
    DocumentInput,
    SummarizeInput,
    EntityExtractionInput,
    ToolResult,
    SearchTool,
    DocumentTool,
    AnalysisTool,
    ToolRegistry,
    create_tool_registry,
)

__all__ = [
    # Base agent
    "RAGAgent",
    "AgentDependencies",
    "ConversationManager",
    "ToolUsageTracker",
    "create_research_agent",
    "create_qa_agent",
    "create_creative_agent",
    
    # Configuration
    "AgentConfig",
    "ModelConfig",
    "PromptConfig",
    "BehaviorConfig",
    "SearchConfig",
    "ToolConfig",
    "get_research_agent_config",
    "get_qa_agent_config",
    "get_creative_agent_config",
    
    # Prompts
    "get_system_prompt",
    "format_system_prompt",
    "create_search_refinement_prompt",
    "create_synthesis_prompt",
    "create_tool_selection_prompt",
    "PROMPT_REGISTRY",
    "TOOL_DESCRIPTIONS",
    
    # Tools
    "SearchInput",
    "DocumentInput",
    "SummarizeInput",
    "EntityExtractionInput",
    "ToolResult",
    "SearchTool",
    "DocumentTool",
    "AnalysisTool",
    "ToolRegistry",
    "create_tool_registry",
]