"""
Base agent implementation using Pydantic AI.

This module provides the core agent class that integrates with Pydantic AI,
managing tools, dependencies, and conversation flow.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, AsyncIterator, Callable
from datetime import datetime
from dataclasses import dataclass
import json

from pydantic_ai import Agent, RunContext
from pydantic import BaseModel, Field

from .config import AgentConfig
from .prompts import get_system_prompt, create_synthesis_prompt
from .tools import (
    ToolRegistry, SearchInput, DocumentInput, 
    SummarizeInput, EntityExtractionInput, ToolResult
)
from ..providers.base import LLMProvider

logger = logging.getLogger(__name__)


@dataclass
class AgentDependencies:
    """
    Dependencies injected into the agent context.
    
    These dependencies are passed to tools and can be used to maintain
    session state, user preferences, and other contextual information.
    """
    session_id: str
    user_id: Optional[str] = None
    tool_registry: Optional[ToolRegistry] = None
    config: Optional[AgentConfig] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        """Initialize default values."""
        if self.metadata is None:
            self.metadata = {}


class ConversationManager:
    """
    Manages conversation history and context.
    """
    
    def __init__(self, max_history: int = 20):
        self.max_history = max_history
        self.conversations: Dict[str, List[Dict[str, str]]] = {}
    
    def add_message(self, session_id: str, role: str, content: str):
        """Add a message to conversation history."""
        if session_id not in self.conversations:
            self.conversations[session_id] = []
        
        self.conversations[session_id].append({
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        })
        
        # Trim history if needed
        if len(self.conversations[session_id]) > self.max_history:
            self.conversations[session_id] = self.conversations[session_id][-self.max_history:]
    
    def get_history(self, session_id: str) -> List[Dict[str, str]]:
        """Get conversation history for a session."""
        return self.conversations.get(session_id, [])
    
    def clear_history(self, session_id: str):
        """Clear conversation history for a session."""
        if session_id in self.conversations:
            del self.conversations[session_id]


class ToolUsageTracker:
    """
    Tracks tool usage statistics.
    """
    
    def __init__(self):
        self.usage_stats: Dict[str, Dict[str, Any]] = {}
    
    def track_usage(self, session_id: str, tool_name: str, success: bool, execution_time: float):
        """Track a tool usage event."""
        if session_id not in self.usage_stats:
            self.usage_stats[session_id] = {}
        
        if tool_name not in self.usage_stats[session_id]:
            self.usage_stats[session_id][tool_name] = {
                "total_calls": 0,
                "successful_calls": 0,
                "failed_calls": 0,
                "total_execution_time": 0.0,
                "avg_execution_time": 0.0
            }
        
        stats = self.usage_stats[session_id][tool_name]
        stats["total_calls"] += 1
        if success:
            stats["successful_calls"] += 1
        else:
            stats["failed_calls"] += 1
        stats["total_execution_time"] += execution_time
        stats["avg_execution_time"] = stats["total_execution_time"] / stats["total_calls"]
    
    def get_stats(self, session_id: str) -> Dict[str, Any]:
        """Get usage statistics for a session."""
        return self.usage_stats.get(session_id, {})


class RAGAgent:
    """
    Main RAG agent implementation using Pydantic AI.
    
    This agent provides:
    - Flexible tool registration and execution
    - Conversation management
    - Streaming responses
    - Tool usage tracking
    - Configurable behavior
    """
    
    def __init__(
        self,
        config: AgentConfig,
        llm_provider: LLMProvider,
        tool_registry: Optional[ToolRegistry] = None,
        conversation_manager: Optional[ConversationManager] = None,
        usage_tracker: Optional[ToolUsageTracker] = None
    ):
        """
        Initialize the RAG agent.
        
        Args:
            config: Agent configuration
            llm_provider: LLM provider instance
            tool_registry: Optional tool registry (creates empty if not provided)
            conversation_manager: Optional conversation manager
            usage_tracker: Optional usage tracker
        """
        self.config = config
        self.llm_provider = llm_provider
        self.tool_registry = tool_registry or ToolRegistry()
        self.conversation_manager = conversation_manager or ConversationManager(
            max_history=config.behavior.max_conversation_length
        )
        self.usage_tracker = usage_tracker or ToolUsageTracker()
        
        # Get appropriate system prompt
        prompt_type = self._get_prompt_type()
        system_prompt = get_system_prompt(prompt_type, agent_name=config.name)
        
        # Initialize Pydantic AI agent
        self.agent = Agent(
            model=llm_provider.get_model_string(),
            deps_type=AgentDependencies,
            system_prompt=system_prompt
        )
        
        # Register tools with the agent
        self._register_tools()
        
        logger.info(f"Initialized {config.name} with {len(self.tool_registry.list_tools())} tools")
    
    def _get_prompt_type(self) -> str:
        """Determine prompt type based on agent name/description."""
        name_lower = self.config.name.lower()
        desc_lower = self.config.description.lower()
        
        if "research" in name_lower or "research" in desc_lower:
            return "research"
        elif "q&a" in name_lower or "qa" in name_lower:
            return "qa"
        elif "creative" in name_lower or "creative" in desc_lower:
            return "creative"
        else:
            return "base"
    
    def _register_tools(self):
        """Register tools with the Pydantic AI agent."""
        
        # Search tool
        if "search" in self.tool_registry.tools:
            @self.agent.tool
            async def search(
                ctx: RunContext[AgentDependencies],
                query: str,
                limit: int = 10,
                search_type: Literal["vector", "keyword", "hybrid"] = "hybrid"
            ) -> Dict[str, Any]:
                """Search the knowledge base."""
                start_time = datetime.now()
                
                search_input = SearchInput(
                    query=query,
                    limit=limit,
                    search_type=search_type
                )
                
                result = await ctx.deps.tool_registry.get("search").search(search_input)
                
                # Track usage
                if ctx.deps.config.behavior.track_tool_usage:
                    self.usage_tracker.track_usage(
                        ctx.deps.session_id,
                        "search",
                        result.success,
                        result.execution_time
                    )
                
                if result.success:
                    return result.data
                else:
                    logger.error(f"Search failed: {result.error}")
                    return {"error": result.error}
        
        # Document retrieval tool
        if "get_document" in self.tool_registry.tools:
            @self.agent.tool
            async def get_document(
                ctx: RunContext[AgentDependencies],
                document_id: str,
                include_chunks: bool = False
            ) -> Dict[str, Any]:
                """Retrieve a document by ID."""
                doc_input = DocumentInput(
                    document_id=document_id,
                    include_chunks=include_chunks
                )
                
                result = await ctx.deps.tool_registry.get("get_document")(doc_input)
                
                if ctx.deps.config.behavior.track_tool_usage:
                    self.usage_tracker.track_usage(
                        ctx.deps.session_id,
                        "get_document",
                        result.success,
                        result.execution_time
                    )
                
                if result.success:
                    return result.data
                else:
                    return {"error": result.error}
        
        # List documents tool
        if "list_documents" in self.tool_registry.tools:
            @self.agent.tool
            async def list_documents(
                ctx: RunContext[AgentDependencies],
                limit: int = 20,
                offset: int = 0
            ) -> List[Dict[str, Any]]:
                """List available documents."""
                result = await ctx.deps.tool_registry.get("list_documents")(
                    limit=limit,
                    offset=offset
                )
                
                if ctx.deps.config.behavior.track_tool_usage:
                    self.usage_tracker.track_usage(
                        ctx.deps.session_id,
                        "list_documents",
                        result.success,
                        result.execution_time
                    )
                
                if result.success:
                    return result.data
                else:
                    return []
        
        # Summarize tool
        if "summarize" in self.tool_registry.tools:
            @self.agent.tool
            async def summarize(
                ctx: RunContext[AgentDependencies],
                text: str,
                max_length: int = 500
            ) -> str:
                """Summarize provided text."""
                summarize_input = SummarizeInput(
                    text=text,
                    max_length=max_length
                )
                
                result = await ctx.deps.tool_registry.get("summarize")(summarize_input)
                
                if ctx.deps.config.behavior.track_tool_usage:
                    self.usage_tracker.track_usage(
                        ctx.deps.session_id,
                        "summarize",
                        result.success,
                        result.execution_time
                    )
                
                if result.success:
                    return result.data["summary"]
                else:
                    return f"Summarization failed: {result.error}"
        
        # Entity extraction tool
        if "extract_entities" in self.tool_registry.tools:
            @self.agent.tool
            async def extract_entities(
                ctx: RunContext[AgentDependencies],
                text: str,
                entity_types: Optional[List[str]] = None
            ) -> Dict[str, List[str]]:
                """Extract named entities from text."""
                entity_input = EntityExtractionInput(
                    text=text,
                    entity_types=entity_types
                )
                
                result = await ctx.deps.tool_registry.get("extract_entities")(entity_input)
                
                if ctx.deps.config.behavior.track_tool_usage:
                    self.usage_tracker.track_usage(
                        ctx.deps.session_id,
                        "extract_entities",
                        result.success,
                        result.execution_time
                    )
                
                if result.success:
                    return result.data["entities"]
                else:
                    return {}
    
    async def query(
        self,
        message: str,
        session_id: str,
        user_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process a query and return a response.
        
        Args:
            message: User message
            session_id: Session identifier
            user_id: Optional user identifier
            metadata: Optional metadata
        
        Returns:
            Response dictionary with answer and metadata
        """
        start_time = datetime.now()
        
        # Add message to conversation history
        self.conversation_manager.add_message(session_id, "user", message)
        
        # Create dependencies
        deps = AgentDependencies(
            session_id=session_id,
            user_id=user_id,
            tool_registry=self.tool_registry,
            config=self.config,
            metadata=metadata or {}
        )
        
        try:
            # Get conversation history
            history = self.conversation_manager.get_history(session_id)
            
            # Run the agent
            result = await self.agent.run(
                message,
                deps=deps,
                message_history=history[:-1] if history else []  # Exclude the current message
            )
            
            # Add response to history
            self.conversation_manager.add_message(session_id, "assistant", result.data)
            
            # Prepare response
            response = {
                "answer": result.data,
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "execution_time": (datetime.now() - start_time).total_seconds(),
                "model_usage": getattr(result, "usage", {}),
                "tool_usage": self.usage_tracker.get_stats(session_id) if self.config.behavior.track_tool_usage else {}
            }
            
            return response
            
        except Exception as e:
            logger.error(f"Query processing error: {str(e)}")
            error_response = f"I encountered an error processing your request: {str(e)}"
            self.conversation_manager.add_message(session_id, "assistant", error_response)
            
            return {
                "answer": error_response,
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "execution_time": (datetime.now() - start_time).total_seconds(),
                "error": str(e)
            }
    
    async def stream_query(
        self,
        message: str,
        session_id: str,
        user_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> AsyncIterator[str]:
        """
        Process a query and stream the response.
        
        Args:
            message: User message
            session_id: Session identifier
            user_id: Optional user identifier
            metadata: Optional metadata
        
        Yields:
            Response chunks as they're generated
        """
        # Add message to conversation history
        self.conversation_manager.add_message(session_id, "user", message)
        
        # Create dependencies
        deps = AgentDependencies(
            session_id=session_id,
            user_id=user_id,
            tool_registry=self.tool_registry,
            config=self.config,
            metadata=metadata or {}
        )
        
        try:
            # Get conversation history
            history = self.conversation_manager.get_history(session_id)
            
            # Stream the response
            full_response = ""
            async with self.agent.run_stream(
                message,
                deps=deps,
                message_history=history[:-1] if history else []
            ) as result:
                async for chunk in result:
                    full_response += chunk
                    yield chunk
            
            # Add complete response to history
            self.conversation_manager.add_message(session_id, "assistant", full_response)
            
        except Exception as e:
            logger.error(f"Streaming error: {str(e)}")
            error_message = f"\n\nError: {str(e)}"
            yield error_message
    
    def get_conversation_history(self, session_id: str) -> List[Dict[str, str]]:
        """Get conversation history for a session."""
        return self.conversation_manager.get_history(session_id)
    
    def clear_conversation(self, session_id: str):
        """Clear conversation history for a session."""
        self.conversation_manager.clear_history(session_id)
    
    def get_tool_stats(self, session_id: str) -> Dict[str, Any]:
        """Get tool usage statistics for a session."""
        return self.usage_tracker.get_stats(session_id)
    
    def get_available_tools(self) -> Dict[str, str]:
        """Get available tools and their descriptions."""
        return self.tool_registry.get_descriptions()


# Factory functions for creating agents with presets

def create_research_agent(
    llm_provider: LLMProvider,
    tool_registry: ToolRegistry
) -> RAGAgent:
    """Create an agent optimized for research tasks."""
    from .config import get_research_agent_config
    config = get_research_agent_config()
    return RAGAgent(config, llm_provider, tool_registry)


def create_qa_agent(
    llm_provider: LLMProvider,
    tool_registry: ToolRegistry
) -> RAGAgent:
    """Create an agent optimized for Q&A tasks."""
    from .config import get_qa_agent_config
    config = get_qa_agent_config()
    return RAGAgent(config, llm_provider, tool_registry)


def create_creative_agent(
    llm_provider: LLMProvider,
    tool_registry: ToolRegistry
) -> RAGAgent:
    """Create an agent optimized for creative tasks."""
    from .config import get_creative_agent_config
    config = get_creative_agent_config()
    return RAGAgent(config, llm_provider, tool_registry)