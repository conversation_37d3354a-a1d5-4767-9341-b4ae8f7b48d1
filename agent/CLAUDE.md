# Agent Module - Claude Instructions

## 🎯 Module Purpose
Core agent implementation with Pydantic AI integration, tool management, conversation tracking, and streaming support.

## 🔧 Key Components

### RAGAgent Class (`base.py`)
Main agent class that orchestrates all interactions.

```python
# Basic usage
agent = RAGAgent(config, llm_provider, tool_registry)
response = await agent.query("User question")

# Streaming usage
async for chunk in agent.stream_query("User question"):
    print(chunk["chunk"], end="", flush=True)
```

### AgentConfig (`config.py`)
Configuration management with sensible defaults.

```python
# Minimal config
config = AgentConfig(name="My Agent")

# Full customization
config = AgentConfig(
    name="Research Assistant",
    model=ModelConfig(
        temperature=0.7,
        max_tokens=2000,
        enable_streaming=True
    ),
    prompts=PromptConfig(
        system_prompt="You are a research assistant...",
        enable_cot=True,
        cot_prompt="Let's think step by step:"
    ),
    search=SearchConfig(
        default_limit=10,
        similarity_threshold=0.7
    )
)
```

### Tool System (`tools.py`)

Tools are async functions that the agent can call.

```python
# Tool signature pattern
async def tool_name(param1: str, param2: int = 10) -> str:
    """
    Tool description (shown to agent).
    
    Args:
        param1: Description for agent
        param2: Optional with default
    
    Returns:
        Human-readable result string
    """
    # Implementation
    return f"Result: {result}"

# Registration
tool_registry.register("tool_name", tool_name)
```

### System Prompts (`prompts.py`)

Pre-configured prompts for different agent types.

```python
# Available prompt styles
RESEARCH_PROMPT = "You are an expert research assistant..."
SUPPORT_PROMPT = "You are a helpful customer support agent..."
QA_PROMPT = "You are a precise Q&A assistant..."

# Custom prompt
config.prompts.system_prompt = "Your custom instructions..."
```

## ⚠️ Module-Specific Rules

### Tool Design
- Tools must be async functions
- Always return human-readable strings
- Handle errors gracefully (return error message, don't raise)
- Include comprehensive docstrings
- Validate inputs before processing

### Conversation Management
- Sessions identified by session_id
- History stored in agent.conversations dict
- Auto-cleanup after 1 hour of inactivity
- Maximum 100 messages per session (configurable)

### Streaming Behavior
- Yields chunks as they arrive from LLM
- Last chunk has `is_final=True`
- Includes metadata in each chunk
- Handles backpressure automatically

## 🐛 Common Issues

### "Tool not found" Errors
```python
# Check tool registration
print(tool_registry.tools.keys())

# Ensure tool is registered before agent creation
tool_registry.register("my_tool", my_tool_func)
agent = RAGAgent(config, llm_provider, tool_registry)
```

### Streaming Not Working
```python
# Enable in config
config.model.enable_streaming = True

# Check provider supports streaming
if not hasattr(llm_provider, 'stream_generate'):
    print("Provider doesn't support streaming")
```

### Memory Issues with Long Conversations
```python
# Clear old conversations
await agent.clear_conversation_history(session_id)

# Or implement auto-cleanup
if len(agent.conversations[session_id]) > 100:
    # Keep last 50 messages
    agent.conversations[session_id] = agent.conversations[session_id][-50:]
```

## 🧪 Testing This Module

```bash
# Run agent tests only
uv run pytest tests/test_agent.py -v

# Test specific functionality
uv run pytest tests/test_agent.py::TestRAGAgent::test_streaming -v
```

### Key Test Scenarios
- Basic query/response
- Streaming responses  
- Tool execution
- Error handling
- Conversation history
- Session management
- Configuration validation

### Mock Setup for Tests
```python
@pytest.fixture
def mock_agent():
    mock_llm = Mock(spec=BaseLLMProvider)
    mock_llm.generate = AsyncMock(return_value={
        "content": "Test response",
        "usage": {"total_tokens": 10}
    })
    
    config = AgentConfig(name="Test Agent")
    registry = ToolRegistry()
    
    return RAGAgent(config, mock_llm, registry)
```

## 📊 Performance Notes

### Response Time Optimization
- Lower temperature = faster responses
- Smaller max_tokens = faster responses
- Streaming = better perceived performance
- Tool calls add latency (minimize chains)

### Memory Usage
- Each session stores full history
- Embedding cache can grow large
- Clear inactive sessions periodically
- Monitor with `len(agent.conversations)`

### Concurrency
- Agent is async-safe
- Multiple queries can run in parallel
- Tools should not block event loop
- Use asyncio.gather for batch operations

## 🔒 Security Considerations

### Input Validation
```python
# Always validate in tools
async def search_tool(query: str) -> str:
    # Sanitize input
    if not validate_query(query):
        return "Invalid search query"
    
    # Proceed with search
    results = await search(query)
    return format_results(results)
```

### Prompt Injection
- System prompts are immutable after init
- User messages sanitized by default
- Tool outputs escaped before returning
- Never execute user-provided code

### Rate Limiting
```python
# Implement at API layer
from src.utils import RateLimiter

rate_limiter = RateLimiter(max_requests=60, window_seconds=60)

if not rate_limiter.is_allowed(user_id):
    raise HTTPException(429, "Rate limit exceeded")
```

## 🔗 Integration Points

### With RAG Module
```python
# Tools typically use RAG components
async def search_documents(query: str) -> str:
    results = await vector_store.search(query)
    return format_results(results)
```

### With Providers
```python
# Agent uses provider interfaces
llm_provider = get_llm_provider()  # Auto-detects from env
agent = RAGAgent(config, llm_provider)
```

### With API
```python
# API exposes agent functionality
@router.post("/chat")
async def chat(request: ChatRequest):
    response = await agent.query(
        request.message,
        session_id=request.session_id
    )
    return ChatResponse(**response)
```

## 📝 Adding New Features

### New Tool Checklist
1. [ ] Define async function with clear signature
2. [ ] Add comprehensive docstring
3. [ ] Implement with error handling
4. [ ] Register in tool_registry
5. [ ] Add tests in test_agent.py
6. [ ] Document in examples/

### New Agent Type
1. [ ] Create new prompt in prompts.py
2. [ ] Define configuration preset
3. [ ] Create factory function
4. [ ] Add example usage
5. [ ] Test with real scenarios

## 🔄 Recent Changes
- 2024-01-27: Added streaming support
- 2024-01-26: Implemented tool retry logic
- 2024-01-25: Added conversation management
- 2024-01-24: Created base agent structure