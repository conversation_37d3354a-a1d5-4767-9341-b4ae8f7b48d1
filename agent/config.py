"""
Agent configuration management.

This module provides configuration classes and utilities for agent setup,
including model selection, tool configuration, and behavior settings.
"""

from typing import Dict, Any, List, Optional, Literal
from pydantic import BaseModel, Field, validator
from pydantic_settings import BaseSettings


class ToolConfig(BaseModel):
    """Configuration for individual agent tools."""
    
    enabled: bool = Field(True, description="Whether the tool is enabled")
    timeout: int = Field(30, description="Tool execution timeout in seconds")
    max_retries: int = Field(3, description="Maximum retry attempts")
    cache_results: bool = Field(True, description="Whether to cache tool results")
    cache_ttl: int = Field(3600, description="Cache TTL in seconds")


class SearchConfig(BaseModel):
    """Configuration for search tools."""
    
    use_vector_search: bool = Field(True, description="Enable vector similarity search")
    use_keyword_search: bool = Field(True, description="Enable keyword/BM25 search")
    use_graph_search: bool = Field(False, description="Enable knowledge graph search")
    vector_weight: float = Field(0.7, ge=0.0, le=1.0, description="Weight for vector search in hybrid mode")
    keyword_weight: float = Field(0.3, ge=0.0, le=1.0, description="Weight for keyword search in hybrid mode")
    default_limit: int = Field(10, ge=1, le=100, description="Default number of results")
    similarity_threshold: float = Field(0.7, ge=0.0, le=1.0, description="Minimum similarity score")
    enable_reranking: bool = Field(True, description="Enable result reranking")
    
    @validator("keyword_weight")
    def validate_weights(cls, v, values):
        """Ensure weights sum to 1.0."""
        if "vector_weight" in values:
            total = values["vector_weight"] + v
            if abs(total - 1.0) > 0.001:  # Allow small floating point errors
                raise ValueError(f"Vector and keyword weights must sum to 1.0, got {total}")
        return v


class PromptConfig(BaseModel):
    """Configuration for agent prompts."""
    
    system_prompt: str = Field(
        default="You are a helpful AI assistant with access to a knowledge base. "
        "Always search for relevant information before answering. "
        "Cite your sources and be transparent about what you know and don't know.",
        description="System prompt for the agent"
    )
    enable_cot: bool = Field(True, description="Enable chain-of-thought reasoning")
    enable_reflection: bool = Field(True, description="Enable self-reflection on responses")
    response_format: Literal["text", "markdown", "json"] = Field("markdown", description="Default response format")
    max_reasoning_steps: int = Field(5, ge=1, le=10, description="Maximum CoT reasoning steps")


class BehaviorConfig(BaseModel):
    """Configuration for agent behavior."""
    
    streaming: bool = Field(True, description="Enable streaming responses")
    verbose: bool = Field(False, description="Enable verbose logging")
    track_tool_usage: bool = Field(True, description="Track tool usage statistics")
    allow_parallel_tools: bool = Field(True, description="Allow parallel tool execution")
    fail_on_tool_error: bool = Field(False, description="Fail entire request on tool error")
    include_sources: bool = Field(True, description="Include sources in responses")
    include_confidence: bool = Field(False, description="Include confidence scores")
    max_conversation_length: int = Field(20, ge=1, le=100, description="Maximum conversation turns to maintain")


class ModelConfig(BaseModel):
    """Configuration for model settings."""
    
    provider: Literal["openai", "anthropic", "local"] = Field("openai", description="LLM provider")
    model: str = Field("gpt-4-turbo-preview", description="Model name")
    temperature: float = Field(0.1, ge=0.0, le=2.0, description="Model temperature")
    max_tokens: Optional[int] = Field(None, ge=1, description="Maximum tokens in response")
    top_p: float = Field(1.0, ge=0.0, le=1.0, description="Top-p sampling parameter")
    frequency_penalty: float = Field(0.0, ge=-2.0, le=2.0, description="Frequency penalty")
    presence_penalty: float = Field(0.0, ge=-2.0, le=2.0, description="Presence penalty")
    seed: Optional[int] = Field(None, description="Random seed for reproducibility")


class AgentConfig(BaseSettings):
    """
    Main agent configuration.
    
    This configuration can be loaded from environment variables or provided directly.
    Environment variables should be prefixed with AGENT_ (e.g., AGENT_NAME).
    """
    
    # Basic settings
    name: str = Field("RAG Assistant", description="Agent name")
    description: str = Field("AI assistant with knowledge base access", description="Agent description")
    version: str = Field("0.1.0", description="Agent version")
    
    # Component configurations
    model: ModelConfig = Field(default_factory=ModelConfig)
    prompts: PromptConfig = Field(default_factory=PromptConfig)
    behavior: BehaviorConfig = Field(default_factory=BehaviorConfig)
    search: SearchConfig = Field(default_factory=SearchConfig)
    
    # Tool configurations
    tools: Dict[str, ToolConfig] = Field(
        default_factory=lambda: {
            "vector_search": ToolConfig(),
            "keyword_search": ToolConfig(),
            "graph_search": ToolConfig(enabled=False),
            "document_retrieve": ToolConfig(),
            "summarize": ToolConfig(),
            "extract_entities": ToolConfig(),
        }
    )
    
    # Performance settings
    request_timeout: int = Field(60, ge=1, description="Overall request timeout in seconds")
    max_concurrent_tools: int = Field(5, ge=1, le=20, description="Maximum concurrent tool executions")
    cache_enabled: bool = Field(True, description="Enable response caching")
    cache_ttl: int = Field(3600, ge=60, description="Default cache TTL in seconds")
    
    class Config:
        env_prefix = "AGENT_"
        env_nested_delimiter = "__"
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"
    
    def get_enabled_tools(self) -> List[str]:
        """Get list of enabled tool names."""
        return [name for name, config in self.tools.items() if config.enabled]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return self.dict(exclude_unset=True)
    
    @classmethod
    def from_minimal(cls, **kwargs) -> "AgentConfig":
        """Create configuration with minimal settings."""
        return cls(**kwargs)
    
    def validate_configuration(self) -> List[str]:
        """Validate configuration and return any issues."""
        issues = []
        
        # Check if at least one search method is enabled
        if not any([self.search.use_vector_search, self.search.use_keyword_search, self.search.use_graph_search]):
            issues.append("At least one search method must be enabled")
        
        # Check tool configurations
        if not self.get_enabled_tools():
            issues.append("At least one tool must be enabled")
        
        # Check model configuration
        if self.model.provider == "openai" and not self.model.model.startswith(("gpt-", "o1-")):
            issues.append(f"Invalid OpenAI model: {self.model.model}")
        
        return issues


# Preset configurations for common use cases

def get_research_agent_config() -> AgentConfig:
    """Configuration optimized for research and analysis."""
    return AgentConfig(
        name="Research Assistant",
        description="AI agent optimized for research and analysis",
        model=ModelConfig(
            temperature=0.1,
            max_tokens=4000,
        ),
        prompts=PromptConfig(
            system_prompt=(
                "You are an expert research assistant. Your goal is to provide "
                "comprehensive, accurate, and well-cited information. Always search "
                "multiple sources and synthesize information critically."
            ),
            enable_cot=True,
            enable_reflection=True,
        ),
        search=SearchConfig(
            default_limit=20,
            similarity_threshold=0.6,
            enable_reranking=True,
        ),
        behavior=BehaviorConfig(
            include_sources=True,
            include_confidence=True,
        )
    )


def get_qa_agent_config() -> AgentConfig:
    """Configuration optimized for Q&A interactions."""
    return AgentConfig(
        name="Q&A Assistant",
        description="AI agent optimized for quick question answering",
        model=ModelConfig(
            temperature=0.0,
            max_tokens=1000,
        ),
        prompts=PromptConfig(
            system_prompt=(
                "You are a helpful Q&A assistant. Provide concise, accurate answers "
                "based on the available knowledge base. Be direct and to the point."
            ),
            enable_cot=False,
            response_format="text",
        ),
        search=SearchConfig(
            default_limit=5,
            similarity_threshold=0.8,
        ),
        behavior=BehaviorConfig(
            streaming=False,
            include_sources=True,
        )
    )


def get_creative_agent_config() -> AgentConfig:
    """Configuration optimized for creative tasks."""
    return AgentConfig(
        name="Creative Assistant",
        description="AI agent optimized for creative and generative tasks",
        model=ModelConfig(
            temperature=0.8,
            max_tokens=2000,
            top_p=0.95,
        ),
        prompts=PromptConfig(
            system_prompt=(
                "You are a creative AI assistant. Use the knowledge base as inspiration "
                "but feel free to be imaginative and generate novel ideas."
            ),
            enable_cot=True,
            enable_reflection=False,
        ),
        search=SearchConfig(
            default_limit=10,
            similarity_threshold=0.5,
        ),
        behavior=BehaviorConfig(
            include_confidence=False,
        )
    )