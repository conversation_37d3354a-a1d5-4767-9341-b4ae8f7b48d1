"""
Agent tools implementation.

This module provides tool implementations for the agent, including search,
retrieval, and analysis capabilities. All tools are designed to work with
Pydantic AI's tool system.
"""

import logging
from typing import List, Dict, Any, Optional, Literal, Tuple
from datetime import datetime
from functools import lru_cache
import asyncio

from pydantic import BaseModel, Field
from tenacity import retry, stop_after_attempt, wait_exponential

from ..rag.search import SearchResult, HybridSearcher, VectorSearcher, KeywordSearcher
from ..rag.storage import VectorStore, DocumentStore
from ..utils.validation import validate_query, sanitize_input

logger = logging.getLogger(__name__)


# Tool Input/Output Models

class SearchInput(BaseModel):
    """Input model for search operations."""
    query: str = Field(..., description="Search query text", min_length=1, max_length=500)
    limit: int = Field(10, description="Maximum number of results", ge=1, le=50)
    search_type: Literal["vector", "keyword", "hybrid"] = Field("hybrid", description="Type of search to perform")
    filters: Optional[Dict[str, Any]] = Field(None, description="Optional metadata filters")
    include_scores: bool = Field(True, description="Include relevance scores in results")


class DocumentInput(BaseModel):
    """Input model for document operations."""
    document_id: str = Field(..., description="Document ID", min_length=1)
    include_chunks: bool = Field(False, description="Include document chunks")
    include_metadata: bool = Field(True, description="Include document metadata")


class SummarizeInput(BaseModel):
    """Input model for summarization."""
    text: str = Field(..., description="Text to summarize", min_length=10, max_length=50000)
    max_length: int = Field(500, description="Maximum summary length", ge=50, le=2000)
    style: Literal["concise", "detailed", "bullet_points"] = Field("concise", description="Summary style")


class EntityExtractionInput(BaseModel):
    """Input model for entity extraction."""
    text: str = Field(..., description="Text to extract entities from", min_length=1, max_length=10000)
    entity_types: Optional[List[str]] = Field(None, description="Specific entity types to extract")
    include_context: bool = Field(True, description="Include surrounding context for entities")


class ToolResult(BaseModel):
    """Standard tool result format."""
    success: bool = Field(..., description="Whether the tool execution was successful")
    data: Any = Field(..., description="Tool result data")
    error: Optional[str] = Field(None, description="Error message if execution failed")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    execution_time: float = Field(..., description="Execution time in seconds")


# Tool Implementations

class SearchTool:
    """
    Unified search tool supporting vector, keyword, and hybrid search.
    """
    
    def __init__(
        self,
        vector_store: Optional[VectorStore] = None,
        keyword_searcher: Optional[KeywordSearcher] = None,
        cache_enabled: bool = True,
        cache_ttl: int = 3600
    ):
        self.vector_store = vector_store
        self.keyword_searcher = keyword_searcher
        self.hybrid_searcher = HybridSearcher(vector_store, keyword_searcher) if vector_store and keyword_searcher else None
        self.cache_enabled = cache_enabled
        self.cache_ttl = cache_ttl
        self._cache: Dict[str, Tuple[datetime, ToolResult]] = {}
    
    async def search(self, input_data: SearchInput) -> ToolResult:
        """
        Perform search based on input parameters.
        
        Args:
            input_data: Search input parameters
        
        Returns:
            ToolResult with search results
        """
        start_time = datetime.now()
        
        try:
            # Validate and sanitize input
            query = sanitize_input(input_data.query)
            if not validate_query(query):
                raise ValueError("Invalid search query")
            
            # Check cache if enabled
            cache_key = f"{input_data.search_type}:{query}:{input_data.limit}"
            if self.cache_enabled and cache_key in self._cache:
                cached_time, cached_result = self._cache[cache_key]
                if (datetime.now() - cached_time).seconds < self.cache_ttl:
                    logger.info(f"Returning cached results for query: {query}")
                    return cached_result
            
            # Perform search based on type
            if input_data.search_type == "vector" and self.vector_store:
                results = await self._vector_search(query, input_data.limit, input_data.filters)
            elif input_data.search_type == "keyword" and self.keyword_searcher:
                results = await self._keyword_search(query, input_data.limit, input_data.filters)
            elif input_data.search_type == "hybrid" and self.hybrid_searcher:
                results = await self._hybrid_search(query, input_data.limit, input_data.filters)
            else:
                raise ValueError(f"Search type '{input_data.search_type}' not available")
            
            # Format results
            formatted_results = self._format_results(results, input_data.include_scores)
            
            # Create result
            result = ToolResult(
                success=True,
                data=formatted_results,
                metadata={
                    "query": query,
                    "search_type": input_data.search_type,
                    "result_count": len(formatted_results),
                    "filters_applied": bool(input_data.filters)
                },
                execution_time=(datetime.now() - start_time).total_seconds()
            )
            
            # Cache result if enabled
            if self.cache_enabled:
                self._cache[cache_key] = (datetime.now(), result)
                # Clean old cache entries
                self._clean_cache()
            
            return result
            
        except Exception as e:
            logger.error(f"Search error: {str(e)}")
            return ToolResult(
                success=False,
                data=None,
                error=str(e),
                metadata={"query": input_data.query, "search_type": input_data.search_type},
                execution_time=(datetime.now() - start_time).total_seconds()
            )
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _vector_search(self, query: str, limit: int, filters: Optional[Dict]) -> List[SearchResult]:
        """Perform vector similarity search with retry logic."""
        searcher = VectorSearcher(self.vector_store)
        return await searcher.search(query, limit=limit, filters=filters)
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _keyword_search(self, query: str, limit: int, filters: Optional[Dict]) -> List[SearchResult]:
        """Perform keyword search with retry logic."""
        return await self.keyword_searcher.search(query, limit=limit, filters=filters)
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _hybrid_search(self, query: str, limit: int, filters: Optional[Dict]) -> List[SearchResult]:
        """Perform hybrid search with retry logic."""
        return await self.hybrid_searcher.search(query, limit=limit, filters=filters)
    
    def _format_results(self, results: List[SearchResult], include_scores: bool) -> List[Dict[str, Any]]:
        """Format search results for output."""
        formatted = []
        for result in results:
            item = {
                "content": result.content,
                "document_id": result.document_id,
                "document_title": result.metadata.get("title", "Untitled"),
                "chunk_id": result.chunk_id,
                "metadata": result.metadata
            }
            if include_scores:
                item["score"] = result.score
            formatted.append(item)
        return formatted
    
    def _clean_cache(self):
        """Remove expired cache entries."""
        current_time = datetime.now()
        expired_keys = [
            key for key, (cached_time, _) in self._cache.items()
            if (current_time - cached_time).seconds >= self.cache_ttl
        ]
        for key in expired_keys:
            del self._cache[key]


class DocumentTool:
    """
    Tool for document retrieval and management.
    """
    
    def __init__(self, document_store: DocumentStore):
        self.document_store = document_store
    
    async def get_document(self, input_data: DocumentInput) -> ToolResult:
        """
        Retrieve a document by ID.
        
        Args:
            input_data: Document retrieval parameters
        
        Returns:
            ToolResult with document data
        """
        start_time = datetime.now()
        
        try:
            # Retrieve document
            document = await self.document_store.get_document(
                input_data.document_id,
                include_chunks=input_data.include_chunks
            )
            
            if not document:
                return ToolResult(
                    success=False,
                    data=None,
                    error=f"Document not found: {input_data.document_id}",
                    metadata={"document_id": input_data.document_id},
                    execution_time=(datetime.now() - start_time).total_seconds()
                )
            
            # Format document data
            doc_data = {
                "id": document["id"],
                "title": document.get("title", "Untitled"),
                "content": document.get("content", ""),
                "source": document.get("source", "Unknown"),
                "created_at": document.get("created_at"),
                "updated_at": document.get("updated_at")
            }
            
            if input_data.include_metadata:
                doc_data["metadata"] = document.get("metadata", {})
            
            if input_data.include_chunks:
                doc_data["chunks"] = document.get("chunks", [])
                doc_data["chunk_count"] = len(doc_data["chunks"])
            
            return ToolResult(
                success=True,
                data=doc_data,
                metadata={
                    "document_id": input_data.document_id,
                    "has_chunks": bool(document.get("chunks"))
                },
                execution_time=(datetime.now() - start_time).total_seconds()
            )
            
        except Exception as e:
            logger.error(f"Document retrieval error: {str(e)}")
            return ToolResult(
                success=False,
                data=None,
                error=str(e),
                metadata={"document_id": input_data.document_id},
                execution_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def list_documents(
        self,
        limit: int = 20,
        offset: int = 0,
        filters: Optional[Dict[str, Any]] = None
    ) -> ToolResult:
        """
        List available documents.
        
        Args:
            limit: Maximum number of documents to return
            offset: Number of documents to skip
            filters: Optional metadata filters
        
        Returns:
            ToolResult with document list
        """
        start_time = datetime.now()
        
        try:
            documents = await self.document_store.list_documents(
                limit=limit,
                offset=offset,
                filters=filters
            )
            
            # Format document list
            doc_list = []
            for doc in documents:
                doc_list.append({
                    "id": doc["id"],
                    "title": doc.get("title", "Untitled"),
                    "source": doc.get("source", "Unknown"),
                    "created_at": doc.get("created_at"),
                    "chunk_count": doc.get("chunk_count", 0),
                    "size_bytes": doc.get("size_bytes", 0)
                })
            
            return ToolResult(
                success=True,
                data=doc_list,
                metadata={
                    "total_count": len(doc_list),
                    "limit": limit,
                    "offset": offset,
                    "has_more": len(doc_list) == limit
                },
                execution_time=(datetime.now() - start_time).total_seconds()
            )
            
        except Exception as e:
            logger.error(f"Document listing error: {str(e)}")
            return ToolResult(
                success=False,
                data=None,
                error=str(e),
                metadata={"limit": limit, "offset": offset},
                execution_time=(datetime.now() - start_time).total_seconds()
            )


class AnalysisTool:
    """
    Tool for text analysis operations like summarization and entity extraction.
    """
    
    def __init__(self, llm_client):
        self.llm_client = llm_client
    
    async def summarize(self, input_data: SummarizeInput) -> ToolResult:
        """
        Summarize provided text.
        
        Args:
            input_data: Summarization parameters
        
        Returns:
            ToolResult with summary
        """
        start_time = datetime.now()
        
        try:
            # Create summarization prompt based on style
            if input_data.style == "bullet_points":
                prompt = f"Summarize the following text in bullet points (max {input_data.max_length} chars):\n\n{input_data.text}"
            elif input_data.style == "detailed":
                prompt = f"Provide a detailed summary of the following text (max {input_data.max_length} chars):\n\n{input_data.text}"
            else:
                prompt = f"Provide a concise summary of the following text (max {input_data.max_length} chars):\n\n{input_data.text}"
            
            # Generate summary
            summary = await self.llm_client.generate(prompt, max_tokens=input_data.max_length // 4)
            
            return ToolResult(
                success=True,
                data={
                    "summary": summary,
                    "original_length": len(input_data.text),
                    "summary_length": len(summary),
                    "compression_ratio": len(summary) / len(input_data.text)
                },
                metadata={
                    "style": input_data.style,
                    "max_length": input_data.max_length
                },
                execution_time=(datetime.now() - start_time).total_seconds()
            )
            
        except Exception as e:
            logger.error(f"Summarization error: {str(e)}")
            return ToolResult(
                success=False,
                data=None,
                error=str(e),
                metadata={"text_length": len(input_data.text)},
                execution_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def extract_entities(self, input_data: EntityExtractionInput) -> ToolResult:
        """
        Extract named entities from text.
        
        Args:
            input_data: Entity extraction parameters
        
        Returns:
            ToolResult with extracted entities
        """
        start_time = datetime.now()
        
        try:
            # Create entity extraction prompt
            entity_types_str = ", ".join(input_data.entity_types) if input_data.entity_types else "all types"
            prompt = f"""Extract named entities ({entity_types_str}) from the following text.
Format as JSON with entity type as key and list of entities as value.
{f'Include surrounding context for each entity.' if input_data.include_context else ''}

Text: {input_data.text}"""
            
            # Extract entities
            response = await self.llm_client.generate(prompt, response_format="json")
            
            # Parse and validate response
            import json
            entities = json.loads(response)
            
            # Count total entities
            total_entities = sum(len(v) if isinstance(v, list) else 1 for v in entities.values())
            
            return ToolResult(
                success=True,
                data={
                    "entities": entities,
                    "total_count": total_entities,
                    "entity_types": list(entities.keys())
                },
                metadata={
                    "text_length": len(input_data.text),
                    "requested_types": input_data.entity_types,
                    "include_context": input_data.include_context
                },
                execution_time=(datetime.now() - start_time).total_seconds()
            )
            
        except Exception as e:
            logger.error(f"Entity extraction error: {str(e)}")
            return ToolResult(
                success=False,
                data=None,
                error=str(e),
                metadata={"text_length": len(input_data.text)},
                execution_time=(datetime.now() - start_time).total_seconds()
            )


# Tool Registry

class ToolRegistry:
    """
    Registry for managing available tools.
    """
    
    def __init__(self):
        self.tools: Dict[str, Any] = {}
        self.tool_descriptions: Dict[str, str] = {}
    
    def register(self, name: str, tool: Any, description: str):
        """Register a tool with the registry."""
        self.tools[name] = tool
        self.tool_descriptions[name] = description
    
    def get(self, name: str) -> Optional[Any]:
        """Get a tool by name."""
        return self.tools.get(name)
    
    def list_tools(self) -> List[str]:
        """List all available tool names."""
        return list(self.tools.keys())
    
    def get_descriptions(self) -> Dict[str, str]:
        """Get all tool descriptions."""
        return self.tool_descriptions.copy()


# Helper function to create tool instances

def create_tool_registry(
    vector_store: Optional[VectorStore] = None,
    document_store: Optional[DocumentStore] = None,
    keyword_searcher: Optional[KeywordSearcher] = None,
    llm_client: Optional[Any] = None,
    enabled_tools: Optional[List[str]] = None
) -> ToolRegistry:
    """
    Create and configure a tool registry.
    
    Args:
        vector_store: Vector store for similarity search
        document_store: Document store for retrieval
        keyword_searcher: Keyword searcher for text search
        llm_client: LLM client for analysis tools
        enabled_tools: List of tools to enable (None = all)
    
    Returns:
        Configured tool registry
    """
    registry = ToolRegistry()
    
    # Search tool
    if vector_store or keyword_searcher:
        search_tool = SearchTool(vector_store, keyword_searcher)
        registry.register("search", search_tool, "Search knowledge base using various methods")
    
    # Document tool
    if document_store:
        doc_tool = DocumentTool(document_store)
        registry.register("get_document", doc_tool.get_document, "Retrieve document by ID")
        registry.register("list_documents", doc_tool.list_documents, "List available documents")
    
    # Analysis tools
    if llm_client:
        analysis_tool = AnalysisTool(llm_client)
        registry.register("summarize", analysis_tool.summarize, "Summarize text content")
        registry.register("extract_entities", analysis_tool.extract_entities, "Extract named entities from text")
    
    # Filter to only enabled tools if specified
    if enabled_tools:
        filtered_tools = {}
        filtered_descriptions = {}
        for tool_name in enabled_tools:
            if tool_name in registry.tools:
                filtered_tools[tool_name] = registry.tools[tool_name]
                filtered_descriptions[tool_name] = registry.tool_descriptions[tool_name]
        registry.tools = filtered_tools
        registry.tool_descriptions = filtered_descriptions
    
    return registry