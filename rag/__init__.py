"""
RAG (Retrieval-Augmented Generation) module.

This module provides comprehensive components for building RAG systems:
- Document ingestion and processing
- Text chunking strategies
- Embedding generation and management
- Vector storage backends
- Search implementations
"""

from .chunking import (
    Chunk,
    ChunkMetadata,
    ChunkingStrategy,
    BaseChunker,
    SlidingWindowChunker,
    SentenceChunker,
    MarkdownChunker,
    SemanticChunker,
    create_chunker,
)

from .embeddings import (
    EmbeddingResult,
    EmbeddingCache,
    EmbeddingGenerator,
    generate_contextual_embedding,
    process_documents_with_embeddings,
)

from .ingestion import (
    DocumentMetadata,
    ProcessedDocument,
    DocumentProcessor,
    TextFileProcessor,
    PDFProcessor,
    ImageProcessor,
    DocumentIngestionPipeline,
    estimate_processing_time,
)

from .storage import (
    StoredDocument,
    StoredChunk,
    SearchFilter,
    VectorStore,
    BaseVectorStore,
    SupabaseVectorStore,
    PgVectorStore,
    DocumentStore,
)

from .search import (
    SearchResult,
    SearchStrategy,
    VectorSearcher,
    KeywordSearcher,
    HybridSearcher,
    RerankedSearcher,
    expand_query_with_synonyms,
    generate_sub_queries,
)

__all__ = [
    # Chunking
    "Chunk",
    "ChunkMetadata",
    "ChunkingStrategy",
    "BaseChunker",
    "SlidingWindowChunker",
    "SentenceChunker",
    "MarkdownChunker",
    "SemanticChunker",
    "create_chunker",
    
    # Embeddings
    "EmbeddingResult",
    "EmbeddingCache",
    "EmbeddingGenerator",
    "generate_contextual_embedding",
    "process_documents_with_embeddings",
    
    # Ingestion
    "DocumentMetadata",
    "ProcessedDocument",
    "DocumentProcessor",
    "TextFileProcessor",
    "PDFProcessor",
    "ImageProcessor",
    "DocumentIngestionPipeline",
    "estimate_processing_time",
    
    # Storage
    "StoredDocument",
    "StoredChunk",
    "SearchFilter",
    "VectorStore",
    "BaseVectorStore",
    "SupabaseVectorStore",
    "PgVectorStore",
    "DocumentStore",
    
    # Search
    "SearchResult",
    "SearchStrategy",
    "VectorSearcher",
    "KeywordSearcher",
    "HybridSearcher",
    "RerankedSearcher",
    "expand_query_with_synonyms",
    "generate_sub_queries",
]