"""
Document chunking strategies for RAG.

This module provides various text chunking strategies optimized for retrieval,
including sliding window, sentence-based, and semantic chunking.
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple, Protocol
from dataclasses import dataclass
import hashlib

from pydantic import BaseModel, Field
import tiktoken
import nltk

logger = logging.getLogger(__name__)


class ChunkMetadata(BaseModel):
    """Metadata for a document chunk."""
    chunk_id: str = Field(..., description="Unique chunk identifier")
    document_id: str = Field(..., description="Parent document ID")
    chunk_index: int = Field(..., description="Index of chunk in document")
    start_char: int = Field(..., description="Starting character position")
    end_char: int = Field(..., description="Ending character position")
    word_count: int = Field(..., description="Number of words in chunk")
    token_count: Optional[int] = Field(None, description="Number of tokens (if calculated)")
    headers: List[str] = Field(default_factory=list, description="Headers found in chunk")
    has_code: bool = Field(False, description="Whether chunk contains code blocks")
    overlap_with_prev: int = Field(0, description="Character overlap with previous chunk")
    overlap_with_next: int = Field(0, description="Character overlap with next chunk")


@dataclass
class Chunk:
    """Document chunk with content and metadata."""
    content: str
    metadata: ChunkMetadata
    
    def __str__(self) -> str:
        return f"Chunk {self.metadata.chunk_index}: {len(self.content)} chars"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "content": self.content,
            "metadata": self.metadata.dict()
        }


class ChunkingStrategy(Protocol):
    """Protocol for chunking strategies."""
    
    def chunk(self, text: str, **kwargs) -> List[Chunk]:
        """Chunk text according to strategy."""
        ...


class BaseChunker:
    """Base class for chunking strategies."""
    
    def __init__(
        self,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        min_chunk_size: int = 100,
        use_token_count: bool = False,
        model_name: str = "gpt-4"
    ):
        self.chunk_size = chunk_size
        self.chunk_overlap = min(chunk_overlap, chunk_size // 2)
        self.min_chunk_size = min_chunk_size
        self.use_token_count = use_token_count
        
        if use_token_count:
            try:
                self.tokenizer = tiktoken.encoding_for_model(model_name)
            except:
                self.tokenizer = tiktoken.get_encoding("cl100k_base")
    
    def _calculate_size(self, text: str) -> int:
        """Calculate size based on character or token count."""
        if self.use_token_count and hasattr(self, 'tokenizer'):
            return len(self.tokenizer.encode(text))
        return len(text)
    
    def _generate_chunk_id(self, document_id: str, chunk_index: int, content: str) -> str:
        """Generate unique chunk ID."""
        content_hash = hashlib.md5(content.encode()).hexdigest()[:8]
        return f"{document_id}_chunk_{chunk_index}_{content_hash}"
    
    def _extract_metadata(self, content: str) -> Dict[str, Any]:
        """Extract metadata from chunk content."""
        # Extract headers
        headers = re.findall(r'^(#{1,6})\s+(.+)$', content, re.MULTILINE)
        header_list = [f"{h[0]} {h[1]}" for h in headers]
        
        # Check for code blocks
        has_code = bool(re.search(r'```[\s\S]*?```', content))
        
        # Word count
        word_count = len(content.split())
        
        # Token count if using tokens
        token_count = None
        if self.use_token_count and hasattr(self, 'tokenizer'):
            token_count = len(self.tokenizer.encode(content))
        
        return {
            "headers": header_list,
            "has_code": has_code,
            "word_count": word_count,
            "token_count": token_count
        }


class SlidingWindowChunker(BaseChunker):
    """Simple sliding window chunking strategy."""
    
    def chunk(self, text: str, document_id: str = "doc") -> List[Chunk]:
        """
        Chunk text using sliding window approach.
        
        Args:
            text: Text to chunk
            document_id: Document identifier
        
        Returns:
            List of chunks
        """
        if not text or not text.strip():
            return []
        
        chunks = []
        text_length = len(text)
        start = 0
        chunk_index = 0
        
        while start < text_length:
            # Calculate end position
            end = min(start + self.chunk_size, text_length)
            
            # Extract chunk
            chunk_content = text[start:end]
            
            # Skip if too small (unless it's the last chunk)
            if len(chunk_content.strip()) < self.min_chunk_size and end < text_length:
                start = end
                continue
            
            # Calculate overlaps
            overlap_with_prev = 0
            if chunk_index > 0 and start > 0:
                overlap_with_prev = min(self.chunk_overlap, start)
            
            overlap_with_next = 0
            if end < text_length:
                overlap_with_next = self.chunk_overlap
            
            # Create metadata
            metadata_dict = self._extract_metadata(chunk_content)
            metadata = ChunkMetadata(
                chunk_id=self._generate_chunk_id(document_id, chunk_index, chunk_content),
                document_id=document_id,
                chunk_index=chunk_index,
                start_char=start,
                end_char=end,
                overlap_with_prev=overlap_with_prev,
                overlap_with_next=overlap_with_next,
                **metadata_dict
            )
            
            # Create chunk
            chunk = Chunk(content=chunk_content.strip(), metadata=metadata)
            chunks.append(chunk)
            
            # Move to next position
            chunk_index += 1
            start = end - self.chunk_overlap if end < text_length else end
        
        return chunks


class SentenceChunker(BaseChunker):
    """Sentence-based chunking that respects sentence boundaries."""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Download punkt tokenizer if not available
        try:
            nltk.data.find('tokenizers/punkt')
        except LookupError:
            nltk.download('punkt', quiet=True)
    
    def chunk(self, text: str, document_id: str = "doc") -> List[Chunk]:
        """
        Chunk text by sentences while respecting size limits.
        
        Args:
            text: Text to chunk
            document_id: Document identifier
        
        Returns:
            List of chunks
        """
        if not text or not text.strip():
            return []
        
        # Tokenize into sentences
        sentences = nltk.sent_tokenize(text)
        
        chunks = []
        current_chunk = []
        current_size = 0
        chunk_index = 0
        char_position = 0
        
        for sentence in sentences:
            sentence_size = self._calculate_size(sentence)
            
            # If single sentence exceeds chunk size, split it
            if sentence_size > self.chunk_size:
                # Finish current chunk if any
                if current_chunk:
                    chunk_content = ' '.join(current_chunk)
                    chunks.append(self._create_chunk(
                        chunk_content, document_id, chunk_index,
                        char_position - current_size, char_position
                    ))
                    chunk_index += 1
                    current_chunk = []
                    current_size = 0
                
                # Split large sentence
                words = sentence.split()
                temp_chunk = []
                temp_size = 0
                
                for word in words:
                    word_size = self._calculate_size(word + ' ')
                    if temp_size + word_size > self.chunk_size and temp_chunk:
                        chunk_content = ' '.join(temp_chunk)
                        chunks.append(self._create_chunk(
                            chunk_content, document_id, chunk_index,
                            char_position - temp_size, char_position
                        ))
                        chunk_index += 1
                        temp_chunk = [word]
                        temp_size = word_size
                    else:
                        temp_chunk.append(word)
                        temp_size += word_size
                
                if temp_chunk:
                    current_chunk = temp_chunk
                    current_size = temp_size
            
            # Add sentence to current chunk if it fits
            elif current_size + sentence_size <= self.chunk_size:
                current_chunk.append(sentence)
                current_size += sentence_size
            
            # Start new chunk
            else:
                if current_chunk:
                    chunk_content = ' '.join(current_chunk)
                    chunks.append(self._create_chunk(
                        chunk_content, document_id, chunk_index,
                        char_position - current_size, char_position
                    ))
                    chunk_index += 1
                
                # Add overlap from previous chunk if configured
                if self.chunk_overlap > 0 and chunks:
                    overlap_sentences = []
                    overlap_size = 0
                    
                    # Add sentences from end of previous chunk
                    for sent in reversed(current_chunk):
                        sent_size = self._calculate_size(sent)
                        if overlap_size + sent_size <= self.chunk_overlap:
                            overlap_sentences.insert(0, sent)
                            overlap_size += sent_size
                        else:
                            break
                    
                    current_chunk = overlap_sentences + [sentence]
                    current_size = overlap_size + sentence_size
                else:
                    current_chunk = [sentence]
                    current_size = sentence_size
            
            char_position += len(sentence) + 1  # +1 for space
        
        # Add final chunk
        if current_chunk:
            chunk_content = ' '.join(current_chunk)
            chunks.append(self._create_chunk(
                chunk_content, document_id, chunk_index,
                char_position - current_size, char_position
            ))
        
        return chunks
    
    def _create_chunk(
        self, 
        content: str, 
        document_id: str, 
        chunk_index: int,
        start_char: int,
        end_char: int
    ) -> Chunk:
        """Create a chunk with metadata."""
        metadata_dict = self._extract_metadata(content)
        metadata = ChunkMetadata(
            chunk_id=self._generate_chunk_id(document_id, chunk_index, content),
            document_id=document_id,
            chunk_index=chunk_index,
            start_char=start_char,
            end_char=end_char,
            **metadata_dict
        )
        return Chunk(content=content.strip(), metadata=metadata)


class MarkdownChunker(BaseChunker):
    """Markdown-aware chunking that respects document structure."""
    
    def chunk(self, text: str, document_id: str = "doc") -> List[Chunk]:
        """
        Chunk markdown text respecting structure (headers, code blocks, etc.).
        
        Args:
            text: Markdown text to chunk
            document_id: Document identifier
        
        Returns:
            List of chunks
        """
        if not text or not text.strip():
            return []
        
        # Split into sections based on headers
        sections = self._split_by_headers(text)
        
        chunks = []
        chunk_index = 0
        
        for section in sections:
            section_size = self._calculate_size(section['content'])
            
            # If section fits in one chunk, add it
            if section_size <= self.chunk_size:
                chunks.append(self._create_section_chunk(
                    section, document_id, chunk_index
                ))
                chunk_index += 1
            
            # Otherwise, split the section
            else:
                section_chunks = self._split_large_section(section, document_id, chunk_index)
                chunks.extend(section_chunks)
                chunk_index += len(section_chunks)
        
        return chunks
    
    def _split_by_headers(self, text: str) -> List[Dict[str, Any]]:
        """Split markdown text by headers."""
        # Pattern to match markdown headers
        header_pattern = r'^(#{1,6})\s+(.+)$'
        
        sections = []
        current_section = {
            'headers': [],
            'content': '',
            'start': 0
        }
        
        lines = text.split('\n')
        current_pos = 0
        
        for i, line in enumerate(lines):
            match = re.match(header_pattern, line)
            
            if match:
                # Save previous section if it has content
                if current_section['content'].strip():
                    current_section['end'] = current_pos
                    sections.append(current_section)
                
                # Start new section
                level = len(match.group(1))
                header_text = match.group(2)
                
                current_section = {
                    'headers': [(level, header_text)],
                    'content': line + '\n',
                    'start': current_pos
                }
            else:
                current_section['content'] += line + '\n'
            
            current_pos += len(line) + 1
        
        # Add final section
        if current_section['content'].strip():
            current_section['end'] = current_pos
            sections.append(current_section)
        
        return sections
    
    def _split_large_section(
        self, 
        section: Dict[str, Any], 
        document_id: str, 
        start_index: int
    ) -> List[Chunk]:
        """Split a large section into multiple chunks."""
        content = section['content']
        
        # Try to split by paragraphs first
        paragraphs = content.split('\n\n')
        
        chunks = []
        current_chunk_content = ''
        current_chunk_headers = section['headers'].copy()
        chunk_index = start_index
        
        for para in paragraphs:
            para_size = self._calculate_size(para)
            current_size = self._calculate_size(current_chunk_content)
            
            # If paragraph fits in current chunk
            if current_size + para_size <= self.chunk_size:
                current_chunk_content += para + '\n\n'
            
            # Start new chunk
            else:
                if current_chunk_content.strip():
                    chunks.append(self._create_section_chunk(
                        {
                            'headers': current_chunk_headers,
                            'content': current_chunk_content,
                            'start': section['start'],
                            'end': section['end']
                        },
                        document_id,
                        chunk_index
                    ))
                    chunk_index += 1
                
                current_chunk_content = para + '\n\n'
                current_chunk_headers = section['headers'].copy()
        
        # Add final chunk
        if current_chunk_content.strip():
            chunks.append(self._create_section_chunk(
                {
                    'headers': current_chunk_headers,
                    'content': current_chunk_content,
                    'start': section['start'],
                    'end': section['end']
                },
                document_id,
                chunk_index
            ))
        
        return chunks
    
    def _create_section_chunk(
        self, 
        section: Dict[str, Any], 
        document_id: str, 
        chunk_index: int
    ) -> Chunk:
        """Create a chunk from a section."""
        content = section['content'].strip()
        metadata_dict = self._extract_metadata(content)
        
        # Add section headers to metadata
        header_strings = [f"{'#' * level} {text}" for level, text in section['headers']]
        metadata_dict['headers'] = header_strings
        
        metadata = ChunkMetadata(
            chunk_id=self._generate_chunk_id(document_id, chunk_index, content),
            document_id=document_id,
            chunk_index=chunk_index,
            start_char=section.get('start', 0),
            end_char=section.get('end', len(content)),
            **metadata_dict
        )
        
        return Chunk(content=content, metadata=metadata)


class SemanticChunker(BaseChunker):
    """
    Semantic chunking that groups related content together.
    
    This is a placeholder for more advanced semantic chunking that would
    use embeddings to group semantically similar sentences/paragraphs.
    """
    
    def __init__(self, embedding_provider=None, similarity_threshold: float = 0.8, **kwargs):
        super().__init__(**kwargs)
        self.embedding_provider = embedding_provider
        self.similarity_threshold = similarity_threshold
    
    def chunk(self, text: str, document_id: str = "doc") -> List[Chunk]:
        """
        Chunk text based on semantic similarity.
        
        Note: This is a simplified implementation. A full implementation
        would use embeddings to calculate similarity between segments.
        
        Args:
            text: Text to chunk
            document_id: Document identifier
        
        Returns:
            List of chunks
        """
        # For now, fall back to sentence chunking
        # In a full implementation, this would:
        # 1. Split text into sentences
        # 2. Generate embeddings for each sentence
        # 3. Group sentences with high similarity
        # 4. Respect size constraints
        
        logger.warning("Semantic chunking not fully implemented, using sentence chunking")
        sentence_chunker = SentenceChunker(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap,
            min_chunk_size=self.min_chunk_size,
            use_token_count=self.use_token_count
        )
        return sentence_chunker.chunk(text, document_id)


# Factory function for creating chunkers

def create_chunker(
    strategy: str = "sliding_window",
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    **kwargs
) -> ChunkingStrategy:
    """
    Create a chunker with the specified strategy.
    
    Args:
        strategy: Chunking strategy (sliding_window, sentence, markdown, semantic)
        chunk_size: Target chunk size
        chunk_overlap: Overlap between chunks
        **kwargs: Additional strategy-specific parameters
    
    Returns:
        Chunker instance
    """
    strategies = {
        "sliding_window": SlidingWindowChunker,
        "sentence": SentenceChunker,
        "markdown": MarkdownChunker,
        "semantic": SemanticChunker,
    }
    
    if strategy not in strategies:
        raise ValueError(f"Unknown chunking strategy: {strategy}")
    
    chunker_class = strategies[strategy]
    return chunker_class(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        **kwargs
    )