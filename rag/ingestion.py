"""
Document ingestion pipeline for RAG.

This module provides a complete pipeline for ingesting documents,
including text extraction, processing, chunking, and embedding generation.
"""

import os
import logging
import hashlib
from typing import List, Dict, Any, Optional, Protocol, Union
from pathlib import Path
from datetime import datetime
import mimetypes
import asyncio

from pydantic import BaseModel, Field
import PyPDF2
import chardet
from PIL import Image
import pytesseract

from .chunking import create_chunker, Chunk, ChunkingStrategy
from .embeddings import EmbeddingGenerator

logger = logging.getLogger(__name__)


class DocumentMetadata(BaseModel):
    """Metadata for ingested documents."""
    document_id: str = Field(..., description="Unique document identifier")
    title: str = Field(..., description="Document title")
    source: str = Field(..., description="Document source (file path or URL)")
    source_type: str = Field(..., description="Type of source (file, url, etc.)")
    file_type: str = Field(..., description="File type/MIME type")
    size_bytes: int = Field(..., description="Document size in bytes")
    created_at: datetime = Field(default_factory=datetime.now, description="Ingestion timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    checksum: str = Field(..., description="Document checksum")
    processing_status: str = Field("pending", description="Processing status")
    error_message: Optional[str] = Field(None, description="Error message if processing failed")
    custom_metadata: Dict[str, Any] = Field(default_factory=dict, description="Custom metadata")


class ProcessedDocument(BaseModel):
    """Processed document with content and metadata."""
    metadata: DocumentMetadata
    content: str = Field(..., description="Extracted text content")
    chunks: List[Chunk] = Field(default_factory=list, description="Document chunks")
    embeddings: Optional[List[List[float]]] = Field(None, description="Chunk embeddings")
    processing_time: float = Field(..., description="Processing time in seconds")


class DocumentProcessor(Protocol):
    """Protocol for document processors."""
    
    def can_process(self, file_path: str) -> bool:
        """Check if processor can handle file."""
        ...
    
    def extract_text(self, file_path: str) -> str:
        """Extract text from file."""
        ...


class TextFileProcessor:
    """Process plain text files."""
    
    SUPPORTED_EXTENSIONS = {'.txt', '.md', '.log', '.csv', '.json', '.xml', '.yaml', '.yml'}
    
    def can_process(self, file_path: str) -> bool:
        """Check if this is a text file."""
        return Path(file_path).suffix.lower() in self.SUPPORTED_EXTENSIONS
    
    def extract_text(self, file_path: str) -> str:
        """Extract text with encoding detection."""
        # Detect encoding
        with open(file_path, 'rb') as f:
            raw_data = f.read()
            result = chardet.detect(raw_data)
            encoding = result['encoding'] or 'utf-8'
        
        # Read with detected encoding
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                text = f.read()
                logger.info(f"Successfully read {file_path} with {encoding} encoding")
                return text
        except UnicodeDecodeError:
            # Fallback to common encodings
            for enc in ['utf-8', 'latin-1', 'cp1252', 'ascii']:
                try:
                    with open(file_path, 'r', encoding=enc) as f:
                        text = f.read()
                        logger.info(f"Successfully read {file_path} with {enc} encoding (fallback)")
                        return text
                except UnicodeDecodeError:
                    continue
            
            raise ValueError(f"Could not decode {file_path} with any supported encoding")


class PDFProcessor:
    """Process PDF files."""
    
    def can_process(self, file_path: str) -> bool:
        """Check if this is a PDF file."""
        return Path(file_path).suffix.lower() == '.pdf'
    
    def extract_text(self, file_path: str) -> str:
        """Extract text from PDF."""
        text_parts = []
        
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                num_pages = len(pdf_reader.pages)
                
                for page_num in range(num_pages):
                    try:
                        page = pdf_reader.pages[page_num]
                        text = page.extract_text()
                        if text.strip():
                            text_parts.append(f"--- Page {page_num + 1} ---\n{text}")
                    except Exception as e:
                        logger.warning(f"Error extracting page {page_num}: {str(e)}")
                
                if not text_parts:
                    raise ValueError("No text could be extracted from PDF")
                
                logger.info(f"Extracted text from {num_pages} pages")
                return '\n\n'.join(text_parts)
                
        except Exception as e:
            logger.error(f"PDF processing error: {str(e)}")
            raise


class ImageProcessor:
    """Process image files using OCR."""
    
    SUPPORTED_EXTENSIONS = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff'}
    
    def __init__(self, use_ocr: bool = True):
        self.use_ocr = use_ocr
        if use_ocr:
            # Check if tesseract is available
            try:
                pytesseract.get_tesseract_version()
            except:
                logger.warning("Tesseract not found, OCR will be disabled")
                self.use_ocr = False
    
    def can_process(self, file_path: str) -> bool:
        """Check if this is an image file."""
        return Path(file_path).suffix.lower() in self.SUPPORTED_EXTENSIONS
    
    def extract_text(self, file_path: str) -> str:
        """Extract text from image using OCR."""
        if not self.use_ocr:
            return f"[Image: {Path(file_path).name}]"
        
        try:
            image = Image.open(file_path)
            text = pytesseract.image_to_string(image)
            
            if not text.strip():
                return f"[Image with no extractable text: {Path(file_path).name}]"
            
            return text
            
        except Exception as e:
            logger.error(f"OCR error for {file_path}: {str(e)}")
            return f"[Image (OCR failed): {Path(file_path).name}]"


class DocumentIngestionPipeline:
    """
    Complete document ingestion pipeline.
    """
    
    def __init__(
        self,
        chunker: Optional[ChunkingStrategy] = None,
        embedding_generator: Optional[EmbeddingGenerator] = None,
        processors: Optional[List[DocumentProcessor]] = None,
        storage_backend=None,
        max_file_size_mb: int = 50,
        generate_embeddings: bool = True
    ):
        # Initialize chunker
        self.chunker = chunker or create_chunker("markdown", chunk_size=1000, chunk_overlap=200)
        
        # Initialize embedding generator
        self.embedding_generator = embedding_generator if generate_embeddings else None
        
        # Initialize processors
        self.processors = processors or [
            TextFileProcessor(),
            PDFProcessor(),
            ImageProcessor(use_ocr=False),  # OCR disabled by default
        ]
        
        self.storage_backend = storage_backend
        self.max_file_size_mb = max_file_size_mb
        self.generate_embeddings = generate_embeddings
    
    async def ingest_file(
        self,
        file_path: Union[str, Path],
        custom_metadata: Optional[Dict[str, Any]] = None
    ) -> ProcessedDocument:
        """
        Ingest a single file.
        
        Args:
            file_path: Path to file
            custom_metadata: Optional custom metadata
        
        Returns:
            Processed document
        """
        start_time = datetime.now()
        file_path = Path(file_path)
        
        # Validate file
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        file_size = file_path.stat().st_size
        if file_size > self.max_file_size_mb * 1024 * 1024:
            raise ValueError(f"File too large: {file_size / 1024 / 1024:.2f}MB "
                           f"(max: {self.max_file_size_mb}MB)")
        
        # Generate document ID
        with open(file_path, 'rb') as f:
            checksum = hashlib.sha256(f.read()).hexdigest()
        document_id = f"{file_path.stem}_{checksum[:8]}"
        
        # Create metadata
        metadata = DocumentMetadata(
            document_id=document_id,
            title=file_path.stem,
            source=str(file_path),
            source_type="file",
            file_type=mimetypes.guess_type(str(file_path))[0] or "unknown",
            size_bytes=file_size,
            checksum=checksum,
            custom_metadata=custom_metadata or {}
        )
        
        try:
            # Find appropriate processor
            processor = self._get_processor(str(file_path))
            if not processor:
                raise ValueError(f"No processor found for file type: {file_path.suffix}")
            
            # Extract text
            logger.info(f"Extracting text from {file_path.name}")
            text = processor.extract_text(str(file_path))
            
            if not text or not text.strip():
                raise ValueError("No text extracted from file")
            
            # Chunk text
            logger.info(f"Chunking text from {file_path.name}")
            chunks = self.chunker.chunk(text, document_id)
            
            # Generate embeddings if enabled
            embeddings = None
            if self.generate_embeddings and self.embedding_generator:
                logger.info(f"Generating embeddings for {len(chunks)} chunks")
                chunk_embeddings = await self.embedding_generator.embed_chunks(chunks)
                embeddings = [emb for _, emb in chunk_embeddings]
            
            # Update metadata
            metadata.processing_status = "completed"
            metadata.updated_at = datetime.now()
            
            # Create processed document
            processing_time = (datetime.now() - start_time).total_seconds()
            processed_doc = ProcessedDocument(
                metadata=metadata,
                content=text,
                chunks=chunks,
                embeddings=embeddings,
                processing_time=processing_time
            )
            
            # Store if backend available
            if self.storage_backend:
                await self._store_document(processed_doc)
            
            logger.info(f"Successfully processed {file_path.name} in {processing_time:.2f}s")
            return processed_doc
            
        except Exception as e:
            # Update metadata with error
            metadata.processing_status = "failed"
            metadata.error_message = str(e)
            metadata.updated_at = datetime.now()
            
            logger.error(f"Failed to process {file_path.name}: {str(e)}")
            
            processing_time = (datetime.now() - start_time).total_seconds()
            return ProcessedDocument(
                metadata=metadata,
                content="",
                chunks=[],
                embeddings=None,
                processing_time=processing_time
            )
    
    async def ingest_directory(
        self,
        directory_path: Union[str, Path],
        recursive: bool = True,
        file_extensions: Optional[List[str]] = None,
        custom_metadata: Optional[Dict[str, Any]] = None
    ) -> List[ProcessedDocument]:
        """
        Ingest all files in a directory.
        
        Args:
            directory_path: Path to directory
            recursive: Whether to process subdirectories
            file_extensions: Optional list of extensions to process
            custom_metadata: Optional custom metadata for all files
        
        Returns:
            List of processed documents
        """
        directory_path = Path(directory_path)
        if not directory_path.is_dir():
            raise ValueError(f"Not a directory: {directory_path}")
        
        # Find all files
        if recursive:
            pattern = "**/*"
        else:
            pattern = "*"
        
        files = []
        for file_path in directory_path.glob(pattern):
            if file_path.is_file():
                # Check extension filter
                if file_extensions:
                    if file_path.suffix.lower() not in file_extensions:
                        continue
                
                # Check if we can process it
                if self._get_processor(str(file_path)):
                    files.append(file_path)
        
        logger.info(f"Found {len(files)} files to process")
        
        # Process files concurrently
        tasks = []
        for file_path in files:
            task = self.ingest_file(file_path, custom_metadata)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter successful results
        processed_docs = []
        for result in results:
            if isinstance(result, ProcessedDocument):
                processed_docs.append(result)
            else:
                logger.error(f"Processing error: {result}")
        
        logger.info(f"Successfully processed {len(processed_docs)}/{len(files)} files")
        return processed_docs
    
    async def ingest_urls(
        self,
        urls: List[str],
        custom_metadata: Optional[Dict[str, Any]] = None
    ) -> List[ProcessedDocument]:
        """
        Ingest content from URLs.
        
        Note: This is a placeholder. Full implementation would include:
        - Web scraping
        - HTML to text conversion
        - Handling different content types
        
        Args:
            urls: List of URLs to ingest
            custom_metadata: Optional custom metadata
        
        Returns:
            List of processed documents
        """
        logger.warning("URL ingestion not implemented in this template")
        return []
    
    def _get_processor(self, file_path: str) -> Optional[DocumentProcessor]:
        """Find appropriate processor for file."""
        for processor in self.processors:
            if processor.can_process(file_path):
                return processor
        return None
    
    async def _store_document(self, document: ProcessedDocument):
        """Store processed document in backend."""
        if not self.storage_backend:
            return
        
        try:
            # Store document metadata
            await self.storage_backend.store_document(
                document_id=document.metadata.document_id,
                metadata=document.metadata.dict(),
                content=document.content
            )
            
            # Store chunks with embeddings
            for i, chunk in enumerate(document.chunks):
                embedding = document.embeddings[i] if document.embeddings else None
                
                await self.storage_backend.store_chunk(
                    chunk_id=chunk.metadata.chunk_id,
                    document_id=document.metadata.document_id,
                    content=chunk.content,
                    embedding=embedding,
                    metadata=chunk.metadata.dict()
                )
            
            logger.info(f"Stored document {document.metadata.document_id} with "
                       f"{len(document.chunks)} chunks")
                       
        except Exception as e:
            logger.error(f"Failed to store document: {str(e)}")
            raise


# Utility functions

def estimate_processing_time(
    file_size_bytes: int,
    file_type: str,
    generate_embeddings: bool = True
) -> float:
    """
    Estimate processing time for a file.
    
    Args:
        file_size_bytes: File size in bytes
        file_type: MIME type or extension
        generate_embeddings: Whether embeddings will be generated
    
    Returns:
        Estimated time in seconds
    """
    # Base estimates (seconds per MB)
    base_rates = {
        "text/plain": 0.1,
        "application/pdf": 0.5,
        "image": 2.0,
        "default": 0.3
    }
    
    # Determine rate
    if "text" in file_type:
        rate = base_rates["text/plain"]
    elif "pdf" in file_type:
        rate = base_rates["application/pdf"]
    elif "image" in file_type:
        rate = base_rates["image"]
    else:
        rate = base_rates["default"]
    
    # Calculate base time
    size_mb = file_size_bytes / (1024 * 1024)
    base_time = size_mb * rate
    
    # Add embedding time if needed
    if generate_embeddings:
        # Rough estimate: 0.1s per chunk, ~10 chunks per MB
        embedding_time = size_mb * 10 * 0.1
        base_time += embedding_time
    
    return max(1.0, base_time)  # Minimum 1 second