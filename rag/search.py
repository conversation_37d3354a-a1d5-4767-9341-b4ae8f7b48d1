"""
Search implementations for RAG.

This module provides different search strategies including vector similarity,
keyword/BM25 search, and hybrid approaches.
"""

import logging
import re
from typing import List, Dict, Any, Optional, Tuple, Protocol
from dataclasses import dataclass
import math
from collections import Counter, defaultdict

import numpy as np
from pydantic import BaseModel, Field

from .storage import VectorStore, StoredChunk, SearchFilter
from .embeddings import EmbeddingGenerator

logger = logging.getLogger(__name__)


class SearchResult(BaseModel):
    """Search result with content and metadata."""
    chunk_id: str = Field(..., description="Chunk ID")
    document_id: str = Field(..., description="Document ID")
    content: str = Field(..., description="Chunk content")
    score: float = Field(..., description="Relevance score")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Result metadata")
    highlights: Optional[List[str]] = Field(None, description="Highlighted snippets")


class SearchStrategy(Protocol):
    """Protocol for search strategies."""
    
    async def search(
        self,
        query: str,
        limit: int = 10,
        filters: Optional[List[SearchFilter]] = None
    ) -> List[SearchResult]:
        """Execute search with the strategy."""
        ...


class VectorSearcher:
    """Vector similarity search implementation."""
    
    def __init__(
        self,
        vector_store: VectorStore,
        embedding_generator: EmbeddingGenerator,
        similarity_threshold: float = 0.0
    ):
        self.vector_store = vector_store
        self.embedding_generator = embedding_generator
        self.similarity_threshold = similarity_threshold
    
    async def search(
        self,
        query: str,
        limit: int = 10,
        filters: Optional[List[SearchFilter]] = None
    ) -> List[SearchResult]:
        """
        Search using vector similarity.
        
        Args:
            query: Search query
            limit: Maximum results
            filters: Optional filters
        
        Returns:
            List of search results
        """
        # Generate query embedding
        embedding_result = await self.embedding_generator.generate_embedding(query)
        query_embedding = embedding_result.embedding
        
        # Search in vector store
        chunk_results = await self.vector_store.search_by_vector(
            query_embedding=query_embedding,
            limit=limit,
            filters=filters
        )
        
        # Convert to search results
        results = []
        for chunk, similarity in chunk_results:
            if similarity >= self.similarity_threshold:
                result = SearchResult(
                    chunk_id=chunk.id,
                    document_id=chunk.document_id,
                    content=chunk.content,
                    score=similarity,
                    metadata={
                        **chunk.metadata,
                        "search_type": "vector",
                        "similarity": similarity
                    }
                )
                results.append(result)
        
        return results


class KeywordSearcher:
    """Keyword/BM25 search implementation."""
    
    def __init__(
        self,
        k1: float = 1.2,
        b: float = 0.75
    ):
        self.k1 = k1  # BM25 term frequency saturation
        self.b = b    # BM25 length normalization
        self.documents: Dict[str, StoredChunk] = {}
        self.doc_lengths: Dict[str, int] = {}
        self.avg_doc_length: float = 0.0
        self.doc_count: int = 0
        self.idf_cache: Dict[str, float] = {}
    
    def index_chunks(self, chunks: List[StoredChunk]):
        """Index chunks for keyword search."""
        total_length = 0
        
        for chunk in chunks:
            self.documents[chunk.id] = chunk
            doc_length = len(self._tokenize(chunk.content))
            self.doc_lengths[chunk.id] = doc_length
            total_length += doc_length
        
        self.doc_count = len(chunks)
        self.avg_doc_length = total_length / self.doc_count if self.doc_count > 0 else 0
        
        # Pre-calculate IDF for all terms
        self._calculate_idf()
    
    def _tokenize(self, text: str) -> List[str]:
        """Simple tokenization (can be improved with proper NLP)."""
        # Convert to lowercase and split on non-alphanumeric
        text = text.lower()
        tokens = re.findall(r'\b\w+\b', text)
        return tokens
    
    def _calculate_idf(self):
        """Calculate IDF for all terms."""
        # Count document frequency for each term
        doc_freq = defaultdict(int)
        
        for chunk in self.documents.values():
            terms = set(self._tokenize(chunk.content))
            for term in terms:
                doc_freq[term] += 1
        
        # Calculate IDF
        for term, freq in doc_freq.items():
            self.idf_cache[term] = math.log((self.doc_count - freq + 0.5) / (freq + 0.5) + 1)
    
    def _calculate_bm25_score(self, chunk_id: str, query_terms: List[str]) -> float:
        """Calculate BM25 score for a document."""
        chunk = self.documents[chunk_id]
        doc_terms = self._tokenize(chunk.content)
        doc_length = self.doc_lengths[chunk_id]
        
        # Count term frequencies
        term_freq = Counter(doc_terms)
        
        score = 0.0
        for term in query_terms:
            if term in term_freq:
                tf = term_freq[term]
                idf = self.idf_cache.get(term, 0)
                
                # BM25 formula
                numerator = tf * (self.k1 + 1)
                denominator = tf + self.k1 * (1 - self.b + self.b * (doc_length / self.avg_doc_length))
                
                score += idf * (numerator / denominator)
        
        return score
    
    async def search(
        self,
        query: str,
        limit: int = 10,
        filters: Optional[List[SearchFilter]] = None
    ) -> List[SearchResult]:
        """
        Search using BM25 keyword matching.
        
        Args:
            query: Search query
            limit: Maximum results
            filters: Optional filters
        
        Returns:
            List of search results
        """
        if not self.documents:
            logger.warning("No documents indexed for keyword search")
            return []
        
        # Tokenize query
        query_terms = self._tokenize(query)
        if not query_terms:
            return []
        
        # Calculate scores for all documents
        scores = []
        for chunk_id, chunk in self.documents.items():
            # Apply filters
            if filters:
                skip = False
                for filter in filters:
                    # Simple equality filter implementation
                    if filter.field in chunk.metadata:
                        if chunk.metadata[filter.field] != filter.value:
                            skip = True
                            break
                if skip:
                    continue
            
            score = self._calculate_bm25_score(chunk_id, query_terms)
            if score > 0:
                scores.append((chunk_id, score))
        
        # Sort by score
        scores.sort(key=lambda x: x[1], reverse=True)
        
        # Convert to search results
        results = []
        for chunk_id, score in scores[:limit]:
            chunk = self.documents[chunk_id]
            
            # Find query term highlights
            highlights = self._find_highlights(chunk.content, query_terms)
            
            result = SearchResult(
                chunk_id=chunk.id,
                document_id=chunk.document_id,
                content=chunk.content,
                score=score,
                metadata={
                    **chunk.metadata,
                    "search_type": "keyword",
                    "bm25_score": score
                },
                highlights=highlights
            )
            results.append(result)
        
        return results
    
    def _find_highlights(self, content: str, query_terms: List[str], context_words: int = 10) -> List[str]:
        """Find highlighted snippets containing query terms."""
        highlights = []
        words = content.split()
        
        for i, word in enumerate(words):
            if word.lower() in query_terms:
                # Extract context around the term
                start = max(0, i - context_words)
                end = min(len(words), i + context_words + 1)
                snippet = ' '.join(words[start:end])
                
                # Highlight the term
                for term in query_terms:
                    snippet = re.sub(
                        rf'\b({re.escape(term)})\b',
                        r'**\1**',
                        snippet,
                        flags=re.IGNORECASE
                    )
                
                highlights.append(f"...{snippet}...")
        
        return highlights[:3]  # Return top 3 highlights


class HybridSearcher:
    """Hybrid search combining vector and keyword strategies."""
    
    def __init__(
        self,
        vector_searcher: VectorSearcher,
        keyword_searcher: KeywordSearcher,
        vector_weight: float = 0.7,
        keyword_weight: float = 0.3
    ):
        self.vector_searcher = vector_searcher
        self.keyword_searcher = keyword_searcher
        self.vector_weight = vector_weight
        self.keyword_weight = keyword_weight
    
    async def search(
        self,
        query: str,
        limit: int = 10,
        filters: Optional[List[SearchFilter]] = None
    ) -> List[SearchResult]:
        """
        Search using hybrid approach.
        
        Args:
            query: Search query
            limit: Maximum results
            filters: Optional filters
        
        Returns:
            List of search results
        """
        # Get results from both searchers
        # Use higher limit to ensure we have enough after merging
        search_limit = limit * 2
        
        vector_results = await self.vector_searcher.search(query, search_limit, filters)
        keyword_results = await self.keyword_searcher.search(query, search_limit, filters)
        
        # Normalize and combine scores
        combined_scores = {}
        all_results = {}
        
        # Process vector results
        if vector_results:
            max_vector_score = max(r.score for r in vector_results)
            for result in vector_results:
                normalized_score = result.score / max_vector_score if max_vector_score > 0 else 0
                combined_scores[result.chunk_id] = self.vector_weight * normalized_score
                all_results[result.chunk_id] = result
        
        # Process keyword results
        if keyword_results:
            max_keyword_score = max(r.score for r in keyword_results)
            for result in keyword_results:
                normalized_score = result.score / max_keyword_score if max_keyword_score > 0 else 0
                
                if result.chunk_id in combined_scores:
                    # Combine scores
                    combined_scores[result.chunk_id] += self.keyword_weight * normalized_score
                    # Merge metadata
                    all_results[result.chunk_id].metadata.update(result.metadata)
                    # Add highlights if available
                    if result.highlights:
                        all_results[result.chunk_id].highlights = result.highlights
                else:
                    combined_scores[result.chunk_id] = self.keyword_weight * normalized_score
                    all_results[result.chunk_id] = result
        
        # Sort by combined score
        sorted_chunks = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)
        
        # Build final results
        results = []
        for chunk_id, score in sorted_chunks[:limit]:
            result = all_results[chunk_id]
            result.score = score
            result.metadata["search_type"] = "hybrid"
            result.metadata["combined_score"] = score
            results.append(result)
        
        return results


class RerankedSearcher:
    """Search with re-ranking using a cross-encoder or LLM."""
    
    def __init__(
        self,
        base_searcher: SearchStrategy,
        reranker_model: Optional[Any] = None,
        initial_results: int = 50
    ):
        self.base_searcher = base_searcher
        self.reranker_model = reranker_model
        self.initial_results = initial_results
    
    async def search(
        self,
        query: str,
        limit: int = 10,
        filters: Optional[List[SearchFilter]] = None
    ) -> List[SearchResult]:
        """
        Search with re-ranking.
        
        Args:
            query: Search query
            limit: Maximum results
            filters: Optional filters
        
        Returns:
            List of re-ranked search results
        """
        # Get initial results
        initial_results = await self.base_searcher.search(
            query,
            self.initial_results,
            filters
        )
        
        if not initial_results:
            return []
        
        # Re-rank results
        if self.reranker_model:
            reranked_results = await self._rerank_with_model(query, initial_results)
        else:
            # Simple re-ranking based on content overlap
            reranked_results = self._rerank_simple(query, initial_results)
        
        return reranked_results[:limit]
    
    async def _rerank_with_model(
        self,
        query: str,
        results: List[SearchResult]
    ) -> List[SearchResult]:
        """Re-rank using a cross-encoder model."""
        # Placeholder for cross-encoder re-ranking
        # In a real implementation, this would use a model like MS MARCO
        logger.warning("Model-based re-ranking not implemented")
        return results
    
    def _rerank_simple(
        self,
        query: str,
        results: List[SearchResult]
    ) -> List[SearchResult]:
        """Simple re-ranking based on query-content overlap."""
        query_terms = set(query.lower().split())
        
        for result in results:
            content_terms = set(result.content.lower().split())
            overlap = len(query_terms & content_terms)
            
            # Boost score based on term overlap
            overlap_boost = overlap / len(query_terms) if query_terms else 0
            result.score = result.score * (1 + 0.5 * overlap_boost)
            result.metadata["rerank_overlap"] = overlap
        
        # Re-sort by new scores
        results.sort(key=lambda x: x.score, reverse=True)
        
        return results


# Query enhancement utilities

def expand_query_with_synonyms(query: str, synonym_dict: Optional[Dict[str, List[str]]] = None) -> str:
    """
    Expand query with synonyms.
    
    Args:
        query: Original query
        synonym_dict: Optional synonym dictionary
    
    Returns:
        Expanded query
    """
    if not synonym_dict:
        # Simple example synonyms
        synonym_dict = {
            "create": ["make", "build", "construct", "generate"],
            "delete": ["remove", "erase", "drop", "destroy"],
            "update": ["modify", "change", "edit", "alter"],
            "find": ["search", "locate", "discover", "retrieve"],
        }
    
    words = query.lower().split()
    expanded_words = []
    
    for word in words:
        expanded_words.append(word)
        if word in synonym_dict:
            expanded_words.extend(synonym_dict[word][:2])  # Add top 2 synonyms
    
    return ' '.join(expanded_words)


def generate_sub_queries(query: str) -> List[str]:
    """
    Generate sub-queries for complex questions.
    
    Args:
        query: Original query
    
    Returns:
        List of sub-queries
    """
    sub_queries = [query]  # Always include original
    
    # Extract key phrases
    # This is a simple implementation - could use NLP for better results
    if " and " in query.lower():
        parts = query.split(" and ")
        sub_queries.extend(parts)
    
    if " or " in query.lower():
        parts = query.split(" or ")
        sub_queries.extend(parts)
    
    # Extract questions if multiple
    if query.count("?") > 1:
        questions = [q.strip() + "?" for q in query.split("?") if q.strip()]
        sub_queries.extend(questions)
    
    return list(set(sub_queries))  # Remove duplicates