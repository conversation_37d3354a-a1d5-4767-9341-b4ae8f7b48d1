"""
Embedding generation and management for RAG.

This module provides embedding generation with caching, batch processing,
and support for multiple embedding providers.
"""

import logging
import hashlib
import json
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import asyncio
from pathlib import Path
import pickle

import numpy as np
from pydantic import BaseModel, Field
from tenacity import retry, stop_after_attempt, wait_exponential

from ..providers import EmbeddingProvider, get_embedding_provider
from .chunking import Chunk

logger = logging.getLogger(__name__)


class EmbeddingResult(BaseModel):
    """Result of embedding generation."""
    text: str = Field(..., description="Original text")
    embedding: List[float] = Field(..., description="Embedding vector")
    model: str = Field(..., description="Model used for embedding")
    dimensions: int = Field(..., description="Embedding dimensions")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class EmbeddingCache:
    """Simple file-based embedding cache."""
    
    def __init__(self, cache_dir: Optional[Path] = None, ttl_hours: int = 24 * 7):
        self.cache_dir = cache_dir or Path("./embeddings_cache")
        self.cache_dir.mkdir(exist_ok=True)
        self.ttl = timedelta(hours=ttl_hours)
        self.index_file = self.cache_dir / "index.json"
        self.index = self._load_index()
    
    def _load_index(self) -> Dict[str, Dict[str, Any]]:
        """Load cache index."""
        if self.index_file.exists():
            try:
                with open(self.index_file, 'r') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def _save_index(self):
        """Save cache index."""
        with open(self.index_file, 'w') as f:
            json.dump(self.index, f)
    
    def _get_cache_key(self, text: str, model: str) -> str:
        """Generate cache key for text and model."""
        content = f"{model}:{text}"
        return hashlib.sha256(content.encode()).hexdigest()
    
    def get(self, text: str, model: str) -> Optional[List[float]]:
        """Get embedding from cache if exists and not expired."""
        key = self._get_cache_key(text, model)
        
        if key in self.index:
            entry = self.index[key]
            created = datetime.fromisoformat(entry['created'])
            
            # Check if expired
            if datetime.now() - created > self.ttl:
                self.remove(text, model)
                return None
            
            # Load embedding
            cache_file = self.cache_dir / f"{key}.pkl"
            if cache_file.exists():
                try:
                    with open(cache_file, 'rb') as f:
                        return pickle.load(f)
                except:
                    self.remove(text, model)
        
        return None
    
    def put(self, text: str, model: str, embedding: List[float]):
        """Store embedding in cache."""
        key = self._get_cache_key(text, model)
        
        # Save embedding
        cache_file = self.cache_dir / f"{key}.pkl"
        with open(cache_file, 'wb') as f:
            pickle.dump(embedding, f)
        
        # Update index
        self.index[key] = {
            'created': datetime.now().isoformat(),
            'model': model,
            'text_len': len(text),
            'dimensions': len(embedding)
        }
        self._save_index()
    
    def remove(self, text: str, model: str):
        """Remove embedding from cache."""
        key = self._get_cache_key(text, model)
        
        if key in self.index:
            del self.index[key]
            self._save_index()
        
        cache_file = self.cache_dir / f"{key}.pkl"
        if cache_file.exists():
            cache_file.unlink()
    
    def clear_expired(self):
        """Clear expired entries from cache."""
        expired_keys = []
        
        for key, entry in self.index.items():
            created = datetime.fromisoformat(entry['created'])
            if datetime.now() - created > self.ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            cache_file = self.cache_dir / f"{key}.pkl"
            if cache_file.exists():
                cache_file.unlink()
            del self.index[key]
        
        if expired_keys:
            self._save_index()
            logger.info(f"Cleared {len(expired_keys)} expired cache entries")


class EmbeddingGenerator:
    """
    Main embedding generator with caching and batch processing.
    """
    
    def __init__(
        self,
        provider: Optional[EmbeddingProvider] = None,
        cache_enabled: bool = True,
        cache_dir: Optional[Path] = None,
        batch_size: int = 100,
        max_retries: int = 3,
        show_progress: bool = True
    ):
        self.provider = provider or get_embedding_provider()
        self.cache = EmbeddingCache(cache_dir) if cache_enabled else None
        self.batch_size = batch_size
        self.max_retries = max_retries
        self.show_progress = show_progress
        
        # Get model info
        self.model = self.provider.model_info.name
        self.dimensions = self.provider.model_info.embedding_dimensions or 1536
    
    async def generate_embedding(self, text: str, use_cache: bool = True) -> EmbeddingResult:
        """
        Generate embedding for a single text.
        
        Args:
            text: Text to embed
            use_cache: Whether to use cache
        
        Returns:
            Embedding result
        """
        # Check cache first
        if use_cache and self.cache:
            cached_embedding = self.cache.get(text, self.model)
            if cached_embedding is not None:
                logger.debug(f"Cache hit for text of length {len(text)}")
                return EmbeddingResult(
                    text=text,
                    embedding=cached_embedding,
                    model=self.model,
                    dimensions=len(cached_embedding),
                    metadata={"from_cache": True}
                )
        
        # Generate embedding
        try:
            embeddings = await self.provider.embed_batch([text], batch_size=1)
            embedding = embeddings[0]
            
            # Cache the result
            if use_cache and self.cache:
                self.cache.put(text, self.model, embedding)
            
            return EmbeddingResult(
                text=text,
                embedding=embedding,
                model=self.model,
                dimensions=len(embedding),
                metadata={"from_cache": False}
            )
            
        except Exception as e:
            logger.error(f"Failed to generate embedding: {str(e)}")
            raise
    
    async def generate_embeddings(
        self,
        texts: List[str],
        use_cache: bool = True
    ) -> List[EmbeddingResult]:
        """
        Generate embeddings for multiple texts with batch processing.
        
        Args:
            texts: List of texts to embed
            use_cache: Whether to use cache
        
        Returns:
            List of embedding results
        """
        results = []
        texts_to_embed = []
        text_indices = []
        
        # Check cache for each text
        for i, text in enumerate(texts):
            if use_cache and self.cache:
                cached_embedding = self.cache.get(text, self.model)
                if cached_embedding is not None:
                    results.append((i, EmbeddingResult(
                        text=text,
                        embedding=cached_embedding,
                        model=self.model,
                        dimensions=len(cached_embedding),
                        metadata={"from_cache": True}
                    )))
                    continue
            
            texts_to_embed.append(text)
            text_indices.append(i)
        
        # Generate embeddings for uncached texts
        if texts_to_embed:
            if self.show_progress:
                logger.info(f"Generating embeddings for {len(texts_to_embed)} texts "
                          f"({len(results)} cached)")
            
            # Process in batches
            for batch_start in range(0, len(texts_to_embed), self.batch_size):
                batch_end = min(batch_start + self.batch_size, len(texts_to_embed))
                batch_texts = texts_to_embed[batch_start:batch_end]
                batch_indices = text_indices[batch_start:batch_end]
                
                if self.show_progress:
                    logger.info(f"Processing batch {batch_start // self.batch_size + 1}/"
                              f"{(len(texts_to_embed) + self.batch_size - 1) // self.batch_size}")
                
                try:
                    batch_embeddings = await self._generate_batch_with_retry(batch_texts)
                    
                    # Create results and cache
                    for text, embedding, idx in zip(batch_texts, batch_embeddings, batch_indices):
                        if use_cache and self.cache:
                            self.cache.put(text, self.model, embedding)
                        
                        results.append((idx, EmbeddingResult(
                            text=text,
                            embedding=embedding,
                            model=self.model,
                            dimensions=len(embedding),
                            metadata={"from_cache": False}
                        )))
                
                except Exception as e:
                    logger.error(f"Batch embedding failed: {str(e)}")
                    # Generate individual embeddings as fallback
                    for text, idx in zip(batch_texts, batch_indices):
                        try:
                            result = await self.generate_embedding(text, use_cache)
                            results.append((idx, result))
                        except:
                            # Use zero vector as last resort
                            results.append((idx, EmbeddingResult(
                                text=text,
                                embedding=[0.0] * self.dimensions,
                                model=self.model,
                                dimensions=self.dimensions,
                                metadata={"error": "Failed to generate embedding"}
                            )))
        
        # Sort results by original index
        results.sort(key=lambda x: x[0])
        return [result for _, result in results]
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_batch_with_retry(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for a batch with retry logic."""
        return await self.provider.embed_batch(texts, batch_size=len(texts))
    
    async def embed_chunks(
        self,
        chunks: List[Chunk],
        use_cache: bool = True
    ) -> List[Tuple[Chunk, List[float]]]:
        """
        Generate embeddings for document chunks.
        
        Args:
            chunks: List of chunks to embed
            use_cache: Whether to use cache
        
        Returns:
            List of (chunk, embedding) tuples
        """
        texts = [chunk.content for chunk in chunks]
        embedding_results = await self.generate_embeddings(texts, use_cache)
        
        return [(chunk, result.embedding) for chunk, result in zip(chunks, embedding_results)]
    
    def clear_cache(self):
        """Clear the embedding cache."""
        if self.cache:
            self.cache.clear_expired()
    
    def calculate_similarity(
        self,
        embedding1: List[float],
        embedding2: List[float],
        metric: str = "cosine"
    ) -> float:
        """
        Calculate similarity between two embeddings.
        
        Args:
            embedding1: First embedding
            embedding2: Second embedding
            metric: Similarity metric (cosine, euclidean, dot)
        
        Returns:
            Similarity score
        """
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)
        
        if metric == "cosine":
            # Cosine similarity
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return float(dot_product / (norm1 * norm2))
        
        elif metric == "euclidean":
            # Euclidean distance (convert to similarity)
            distance = np.linalg.norm(vec1 - vec2)
            return float(1 / (1 + distance))
        
        elif metric == "dot":
            # Dot product
            return float(np.dot(vec1, vec2))
        
        else:
            raise ValueError(f"Unknown similarity metric: {metric}")
    
    def find_similar(
        self,
        query_embedding: List[float],
        embeddings: List[List[float]],
        top_k: int = 10,
        threshold: float = 0.0,
        metric: str = "cosine"
    ) -> List[Tuple[int, float]]:
        """
        Find similar embeddings to a query.
        
        Args:
            query_embedding: Query embedding
            embeddings: List of embeddings to search
            top_k: Number of top results to return
            threshold: Minimum similarity threshold
            metric: Similarity metric
        
        Returns:
            List of (index, similarity) tuples
        """
        similarities = []
        
        for i, embedding in enumerate(embeddings):
            sim = self.calculate_similarity(query_embedding, embedding, metric)
            if sim >= threshold:
                similarities.append((i, sim))
        
        # Sort by similarity (descending)
        similarities.sort(key=lambda x: x[1], reverse=True)
        
        return similarities[:top_k]


# Contextual embedding enhancement

async def generate_contextual_embedding(
    text: str,
    context: str,
    generator: EmbeddingGenerator,
    weight: float = 0.7
) -> List[float]:
    """
    Generate embedding that includes surrounding context.
    
    Args:
        text: Main text to embed
        context: Surrounding context
        generator: Embedding generator
        weight: Weight for main text (1-weight for context)
    
    Returns:
        Weighted embedding
    """
    # Generate embeddings
    text_result = await generator.generate_embedding(text)
    context_result = await generator.generate_embedding(context)
    
    # Weighted average
    text_embedding = np.array(text_result.embedding)
    context_embedding = np.array(context_result.embedding)
    
    combined = weight * text_embedding + (1 - weight) * context_embedding
    
    # Normalize
    norm = np.linalg.norm(combined)
    if norm > 0:
        combined = combined / norm
    
    return combined.tolist()


# Batch processing utilities

async def process_documents_with_embeddings(
    documents: List[Dict[str, Any]],
    chunker,
    generator: EmbeddingGenerator,
    document_id_field: str = "id",
    content_field: str = "content"
) -> List[Dict[str, Any]]:
    """
    Process documents by chunking and generating embeddings.
    
    Args:
        documents: List of documents
        chunker: Chunking strategy
        generator: Embedding generator
        document_id_field: Field name for document ID
        content_field: Field name for content
    
    Returns:
        List of processed chunks with embeddings
    """
    all_processed = []
    
    for doc in documents:
        doc_id = doc.get(document_id_field, "unknown")
        content = doc.get(content_field, "")
        
        # Chunk the document
        chunks = chunker.chunk(content, doc_id)
        
        # Generate embeddings
        chunk_embeddings = await generator.embed_chunks(chunks)
        
        # Create processed records
        for chunk, embedding in chunk_embeddings:
            processed = {
                "document_id": doc_id,
                "chunk_id": chunk.metadata.chunk_id,
                "content": chunk.content,
                "embedding": embedding,
                "metadata": {
                    **chunk.metadata.dict(),
                    "document_metadata": doc.get("metadata", {})
                }
            }
            all_processed.append(processed)
    
    return all_processed