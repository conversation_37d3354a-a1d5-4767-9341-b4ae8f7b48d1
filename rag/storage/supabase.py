"""
Supabase vector store implementation.

This module provides a vector storage backend using Supabase with pgvector.
"""

import logging
import json
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from supabase import create_client, Client
from tenacity import retry, stop_after_attempt, wait_exponential

from .base import BaseVectorStore, StoredDocument, StoredChunk, SearchFilter

logger = logging.getLogger(__name__)


class SupabaseVectorStore(BaseVectorStore):
    """Supabase vector store implementation."""
    
    def __init__(
        self,
        url: str,
        key: str,
        embedding_dimension: int = 1536,
        documents_table: str = "documents",
        chunks_table: str = "document_chunks"
    ):
        self.url = url
        self.key = key
        self.embedding_dimension = embedding_dimension
        self.documents_table = documents_table
        self.chunks_table = chunks_table
        self.client: Optional[Client] = None
    
    async def initialize(self) -> None:
        """Initialize Supabase client."""
        self.client = create_client(self.url, self.key)
        logger.info("Initialized Supabase vector store")
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def store_document(
        self,
        document_id: str,
        title: str,
        content: str,
        source: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> StoredDocument:
        """Store a document in Supabase."""
        if not self.client:
            raise RuntimeError("Store not initialized")
        
        document_data = {
            "id": document_id,
            "title": title,
            "content": content,
            "source": source,
            "metadata": json.dumps(metadata or {}),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        # Insert or update document
        result = self.client.table(self.documents_table).upsert(document_data).execute()
        
        return StoredDocument(
            id=document_id,
            title=title,
            content=content,
            source=source,
            metadata=metadata or {},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def store_chunk(
        self,
        chunk_id: str,
        document_id: str,
        chunk_index: int,
        content: str,
        embedding: Optional[List[float]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> StoredChunk:
        """Store a chunk in Supabase."""
        if not self.client:
            raise RuntimeError("Store not initialized")
        
        # Validate embedding
        embedding = self._validate_embedding(embedding, self.embedding_dimension)
        
        chunk_data = {
            "id": chunk_id,
            "document_id": document_id,
            "chunk_index": chunk_index,
            "content": content,
            "embedding": embedding,
            "metadata": json.dumps(metadata or {}),
            "created_at": datetime.now().isoformat()
        }
        
        # Insert chunk
        result = self.client.table(self.chunks_table).insert(chunk_data).execute()
        
        return StoredChunk(
            id=chunk_id,
            document_id=document_id,
            chunk_index=chunk_index,
            content=content,
            embedding=embedding,
            metadata=metadata or {},
            created_at=datetime.now()
        )
    
    async def search_by_vector(
        self,
        query_embedding: List[float],
        limit: int = 10,
        filters: Optional[List[SearchFilter]] = None
    ) -> List[Tuple[StoredChunk, float]]:
        """Search chunks by vector similarity using Supabase RPC function."""
        if not self.client:
            raise RuntimeError("Store not initialized")
        
        # Validate query embedding
        query_embedding = self._validate_embedding(query_embedding, self.embedding_dimension)
        
        # Call Supabase RPC function for vector search
        # Note: This assumes you have a function like 'match_chunks' in Supabase
        params = {
            "query_embedding": query_embedding,
            "match_count": limit
        }
        
        # Add filters if provided
        if filters:
            filter_dict = {}
            for f in filters:
                filter_dict[f.field] = f.value
            params["filter"] = json.dumps(filter_dict)
        
        try:
            result = self.client.rpc("match_chunks", params).execute()
            
            # Convert results to StoredChunk objects
            chunks_with_scores = []
            for row in result.data:
                chunk = StoredChunk(
                    id=row["id"],
                    document_id=row["document_id"],
                    chunk_index=row["chunk_index"],
                    content=row["content"],
                    embedding=row.get("embedding"),
                    metadata=json.loads(row.get("metadata", "{}")),
                    created_at=datetime.fromisoformat(row["created_at"])
                )
                similarity = row.get("similarity", 0.0)
                chunks_with_scores.append((chunk, similarity))
            
            return chunks_with_scores
            
        except Exception as e:
            logger.error(f"Vector search error: {str(e)}")
            return []
    
    async def get_document(self, document_id: str) -> Optional[StoredDocument]:
        """Get document by ID."""
        if not self.client:
            raise RuntimeError("Store not initialized")
        
        result = self.client.table(self.documents_table).select("*").eq("id", document_id).execute()
        
        if result.data:
            row = result.data[0]
            return StoredDocument(
                id=row["id"],
                title=row["title"],
                content=row["content"],
                source=row["source"],
                metadata=json.loads(row.get("metadata", "{}")),
                created_at=datetime.fromisoformat(row["created_at"]),
                updated_at=datetime.fromisoformat(row["updated_at"]) if row.get("updated_at") else None
            )
        
        return None
    
    async def get_chunks(self, document_id: str) -> List[StoredChunk]:
        """Get all chunks for a document."""
        if not self.client:
            raise RuntimeError("Store not initialized")
        
        result = self.client.table(self.chunks_table).select("*").eq("document_id", document_id).order("chunk_index").execute()
        
        chunks = []
        for row in result.data:
            chunk = StoredChunk(
                id=row["id"],
                document_id=row["document_id"],
                chunk_index=row["chunk_index"],
                content=row["content"],
                embedding=row.get("embedding"),
                metadata=json.loads(row.get("metadata", "{}")),
                created_at=datetime.fromisoformat(row["created_at"])
            )
            chunks.append(chunk)
        
        return chunks
    
    async def delete_document(self, document_id: str) -> bool:
        """Delete a document and its chunks."""
        if not self.client:
            raise RuntimeError("Store not initialized")
        
        try:
            # Delete chunks first
            self.client.table(self.chunks_table).delete().eq("document_id", document_id).execute()
            
            # Delete document
            self.client.table(self.documents_table).delete().eq("id", document_id).execute()
            
            return True
            
        except Exception as e:
            logger.error(f"Delete error: {str(e)}")
            return False