"""
Qdrant vector store implementation.

This module provides a vector storage backend using Qdrant, a high-performance
vector database with advanced filtering and search capabilities.
"""

import logging
import uuid
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import os

from qdrant_client import QdrantClient
from qdrant_client.models import (
    Distance, VectorParams, PointStruct, Filter, FieldCondition,
    MatchValue, SearchRequest, UpdateStatus, ScoredPoint,
    CollectionStatus, OptimizersConfigDiff, CollectionDescription
)
from qdrant_client.http.exceptions import UnexpectedResponse
from tenacity import retry, stop_after_attempt, wait_exponential

from .base import BaseVectorStore, StoredDocument, StoredChunk, SearchFilter

logger = logging.getLogger(__name__)


class QdrantVectorStore(BaseVectorStore):
    """Qdrant vector store implementation."""
    
    # Distance metric mapping
    DISTANCE_METRICS = {
        "cosine": Distance.COSINE,
        "euclid": Distance.EUCLID,
        "dot": Distance.DOT,
    }
    
    def __init__(
        self,
        url: Optional[str] = None,
        api_key: Optional[str] = None,
        collection_name: str = "agent_documents",
        embedding_dimension: int = 1536,
        distance_metric: str = "cosine",
        on_disk: bool = True,
        **kwargs
    ):
        """
        Initialize Qdrant vector store.
        
        Args:
            url: Qdrant server URL (defaults to env var or localhost)
            api_key: API key for Qdrant Cloud (optional for local)
            collection_name: Name of the collection to use
            embedding_dimension: Dimension of embeddings
            distance_metric: Distance metric (cosine, euclid, dot)
            on_disk: Whether to store vectors on disk (vs memory)
            **kwargs: Additional Qdrant client configuration
        """
        self.url = url or os.getenv("QDRANT_URL", "http://localhost:6333")
        self.api_key = api_key or os.getenv("QDRANT_API_KEY")
        self.collection_name = collection_name
        self.embedding_dimension = embedding_dimension
        self.distance_metric = self.DISTANCE_METRICS.get(
            distance_metric.lower(), 
            Distance.COSINE
        )
        self.on_disk = on_disk
        self.client: Optional[QdrantClient] = None
        
        # Store additional config
        self.client_config = kwargs
    
    async def initialize(self) -> None:
        """Initialize Qdrant client and ensure collection exists."""
        # Create client
        if self.api_key:
            self.client = QdrantClient(
                url=self.url,
                api_key=self.api_key,
                **self.client_config
            )
        else:
            self.client = QdrantClient(
                url=self.url,
                **self.client_config
            )
        
        # Check if collection exists
        try:
            collection_info = self.client.get_collection(self.collection_name)
            logger.info(f"Using existing Qdrant collection: {self.collection_name}")
            
            # Verify dimensions match
            if collection_info.config.params.vectors.size != self.embedding_dimension:
                logger.warning(
                    f"Collection dimension mismatch: "
                    f"expected {self.embedding_dimension}, "
                    f"got {collection_info.config.params.vectors.size}"
                )
        except UnexpectedResponse:
            # Create collection
            await self._create_collection()
            logger.info(f"Created new Qdrant collection: {self.collection_name}")
    
    async def _create_collection(self):
        """Create Qdrant collection with appropriate settings."""
        self.client.create_collection(
            collection_name=self.collection_name,
            vectors_config=VectorParams(
                size=self.embedding_dimension,
                distance=self.distance_metric,
                on_disk=self.on_disk
            ),
            optimizers_config=OptimizersConfigDiff(
                indexing_threshold=20000,
                memmap_threshold=50000
            )
        )
        
        # Create payload indexes for common fields
        self.client.create_payload_index(
            collection_name=self.collection_name,
            field_name="document_id",
            field_schema="keyword"
        )
        
        self.client.create_payload_index(
            collection_name=self.collection_name,
            field_name="chunk_index",
            field_schema="integer"
        )
        
        self.client.create_payload_index(
            collection_name=self.collection_name,
            field_name="source",
            field_schema="keyword"
        )
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def store_document(
        self,
        document_id: str,
        title: str,
        content: str,
        source: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> StoredDocument:
        """Store a document metadata."""
        # Qdrant doesn't have a separate documents table
        # We store document info as metadata with chunks
        # For now, just return the document object
        return StoredDocument(
            id=document_id,
            title=title,
            content=content,
            source=source,
            metadata=metadata or {},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def store_chunk(
        self,
        chunk_id: str,
        document_id: str,
        chunk_index: int,
        content: str,
        embedding: Optional[List[float]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> StoredChunk:
        """Store a chunk with embedding in Qdrant."""
        if not self.client:
            raise RuntimeError("Store not initialized")
        
        if not embedding:
            raise ValueError("Embedding is required for Qdrant storage")
        
        # Validate embedding
        embedding = self._validate_embedding(embedding, self.embedding_dimension)
        
        # Prepare payload
        payload = {
            "chunk_id": chunk_id,
            "document_id": document_id,
            "chunk_index": chunk_index,
            "content": content,
            "source": metadata.get("source", "") if metadata else "",
            "created_at": datetime.now().isoformat(),
            **{f"metadata_{k}": v for k, v in (metadata or {}).items()}
        }
        
        # Create point
        point = PointStruct(
            id=chunk_id,
            vector=embedding,
            payload=payload
        )
        
        # Upsert to Qdrant
        operation_info = self.client.upsert(
            collection_name=self.collection_name,
            points=[point],
            wait=True
        )
        
        if operation_info.status == UpdateStatus.COMPLETED:
            return StoredChunk(
                id=chunk_id,
                document_id=document_id,
                chunk_index=chunk_index,
                content=content,
                embedding=embedding,
                metadata=metadata or {},
                created_at=datetime.now()
            )
        else:
            raise RuntimeError(f"Failed to store chunk: {operation_info}")
    
    async def search_by_vector(
        self,
        query_embedding: List[float],
        limit: int = 10,
        filters: Optional[List[SearchFilter]] = None
    ) -> List[Tuple[StoredChunk, float]]:
        """Search chunks by vector similarity."""
        if not self.client:
            raise RuntimeError("Store not initialized")
        
        # Validate query embedding
        query_embedding = self._validate_embedding(query_embedding, self.embedding_dimension)
        
        # Build Qdrant filter
        qdrant_filter = None
        if filters:
            conditions = []
            for f in filters:
                if f.field.startswith("metadata."):
                    # Handle metadata fields
                    field_name = f"metadata_{f.field.replace('metadata.', '')}"
                else:
                    field_name = f.field
                
                conditions.append(
                    FieldCondition(
                        key=field_name,
                        match=MatchValue(value=f.value)
                    )
                )
            
            if conditions:
                qdrant_filter = Filter(must=conditions)
        
        # Search
        search_result = self.client.search(
            collection_name=self.collection_name,
            query_vector=query_embedding,
            query_filter=qdrant_filter,
            limit=limit,
            with_payload=True,
            with_vectors=False  # We don't need vectors in response
        )
        
        # Convert results
        chunks_with_scores = []
        for point in search_result:
            # Extract metadata
            metadata = {}
            for key, value in point.payload.items():
                if key.startswith("metadata_"):
                    metadata[key.replace("metadata_", "")] = value
            
            chunk = StoredChunk(
                id=point.payload.get("chunk_id", str(point.id)),
                document_id=point.payload["document_id"],
                chunk_index=point.payload["chunk_index"],
                content=point.payload["content"],
                embedding=None,  # Not returned to save bandwidth
                metadata=metadata,
                created_at=datetime.fromisoformat(point.payload["created_at"])
            )
            
            chunks_with_scores.append((chunk, point.score))
        
        return chunks_with_scores
    
    async def get_document(self, document_id: str) -> Optional[StoredDocument]:
        """Get document by ID."""
        # Since Qdrant doesn't store documents separately,
        # we need to get all chunks and reconstruct
        chunks = await self.get_chunks(document_id)
        
        if not chunks:
            return None
        
        # Reconstruct document from chunks
        # Sort by chunk index and concatenate content
        chunks.sort(key=lambda c: c.chunk_index)
        full_content = "\n".join(c.content for c in chunks)
        
        # Get metadata from first chunk
        first_chunk = chunks[0]
        
        return StoredDocument(
            id=document_id,
            title=first_chunk.metadata.get("title", document_id),
            content=full_content,
            source=first_chunk.metadata.get("source", ""),
            metadata=first_chunk.metadata,
            created_at=first_chunk.created_at,
            updated_at=None
        )
    
    async def get_chunks(self, document_id: str) -> List[StoredChunk]:
        """Get all chunks for a document."""
        if not self.client:
            raise RuntimeError("Store not initialized")
        
        # Search for all chunks with this document_id
        # Using scroll to get all results
        chunks = []
        offset = None
        
        while True:
            results, next_offset = self.client.scroll(
                collection_name=self.collection_name,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="document_id",
                            match=MatchValue(value=document_id)
                        )
                    ]
                ),
                limit=100,
                offset=offset,
                with_payload=True,
                with_vectors=True
            )
            
            for point in results:
                # Extract metadata
                metadata = {}
                for key, value in point.payload.items():
                    if key.startswith("metadata_"):
                        metadata[key.replace("metadata_", "")] = value
                
                chunk = StoredChunk(
                    id=point.payload.get("chunk_id", str(point.id)),
                    document_id=point.payload["document_id"],
                    chunk_index=point.payload["chunk_index"],
                    content=point.payload["content"],
                    embedding=point.vector,
                    metadata=metadata,
                    created_at=datetime.fromisoformat(point.payload["created_at"])
                )
                chunks.append(chunk)
            
            if next_offset is None:
                break
                
            offset = next_offset
        
        # Sort by chunk index
        chunks.sort(key=lambda c: c.chunk_index)
        return chunks
    
    async def delete_document(self, document_id: str) -> bool:
        """Delete a document and its chunks."""
        if not self.client:
            raise RuntimeError("Store not initialized")
        
        try:
            # Delete all points with this document_id
            result = self.client.delete(
                collection_name=self.collection_name,
                points_selector=Filter(
                    must=[
                        FieldCondition(
                            key="document_id",
                            match=MatchValue(value=document_id)
                        )
                    ]
                ),
                wait=True
            )
            
            return result.status == UpdateStatus.COMPLETED
            
        except Exception as e:
            logger.error(f"Delete error: {str(e)}")
            return False
    
    async def optimize_collection(self):
        """Optimize collection for better search performance."""
        if not self.client:
            raise RuntimeError("Store not initialized")
        
        # Trigger optimization
        self.client.update_collection(
            collection_name=self.collection_name,
            optimizer_config=OptimizersConfigDiff(
                indexing_threshold=10000
            )
        )
        
        logger.info(f"Triggered optimization for collection: {self.collection_name}")
    
    async def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the collection."""
        if not self.client:
            raise RuntimeError("Store not initialized")
        
        info = self.client.get_collection(self.collection_name)
        
        return {
            "vectors_count": info.vectors_count,
            "points_count": info.points_count,
            "segments_count": info.segments_count,
            "status": info.status,
            "optimizer_status": info.optimizer_status,
            "vector_size": info.config.params.vectors.size,
            "distance": info.config.params.vectors.distance
        }