"""
Base classes and protocols for vector storage.

This module defines the core abstractions for vector storage implementations,
allowing different backends to be used interchangeably.
"""

import logging
from typing import List, Dict, Any, Optional, Protocol, Tuple
from datetime import datetime
from abc import ABC, abstractmethod
import numpy as np
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class StoredDocument(BaseModel):
    """Document stored in the system."""
    id: str = Field(..., description="Document ID")
    title: str = Field(..., description="Document title")
    content: str = Field(..., description="Full document content")
    source: str = Field(..., description="Document source")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Document metadata")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Update timestamp")


class StoredChunk(BaseModel):
    """Chunk stored in the system."""
    id: str = Field(..., description="Chunk ID")
    document_id: str = Field(..., description="Parent document ID")
    chunk_index: int = Field(..., description="Index in document")
    content: str = Field(..., description="Chunk content")
    embedding: Optional[List[float]] = Field(None, description="Embedding vector")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Chunk metadata")
    created_at: datetime = Field(..., description="Creation timestamp")


class SearchFilter(BaseModel):
    """Search filter criteria."""
    field: str = Field(..., description="Field to filter on")
    operator: str = Field("=", description="Filter operator")
    value: Any = Field(..., description="Filter value")


class VectorStore(Protocol):
    """Protocol for vector storage backends."""
    
    async def initialize(self) -> None:
        """Initialize the storage backend."""
        ...
    
    async def store_document(
        self,
        document_id: str,
        title: str,
        content: str,
        source: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> StoredDocument:
        """Store a document."""
        ...
    
    async def store_chunk(
        self,
        chunk_id: str,
        document_id: str,
        chunk_index: int,
        content: str,
        embedding: Optional[List[float]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> StoredChunk:
        """Store a chunk with optional embedding."""
        ...
    
    async def search_by_vector(
        self,
        query_embedding: List[float],
        limit: int = 10,
        filters: Optional[List[SearchFilter]] = None
    ) -> List[Tuple[StoredChunk, float]]:
        """Search chunks by vector similarity."""
        ...
    
    async def get_document(self, document_id: str) -> Optional[StoredDocument]:
        """Get document by ID."""
        ...
    
    async def get_chunks(self, document_id: str) -> List[StoredChunk]:
        """Get all chunks for a document."""
        ...
    
    async def delete_document(self, document_id: str) -> bool:
        """Delete a document and its chunks."""
        ...


class BaseVectorStore(ABC):
    """Base class for vector stores."""
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the storage backend."""
        pass
    
    def _validate_embedding(self, embedding: Optional[List[float]], expected_dim: int) -> Optional[List[float]]:
        """Validate embedding dimensions."""
        if embedding is None:
            return None
        
        if len(embedding) != expected_dim:
            raise ValueError(f"Embedding dimension mismatch: expected {expected_dim}, got {len(embedding)}")
        
        return embedding
    
    def _calculate_cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        arr1 = np.array(vec1)
        arr2 = np.array(vec2)
        
        dot_product = np.dot(arr1, arr2)
        norm1 = np.linalg.norm(arr1)
        norm2 = np.linalg.norm(arr2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return float(dot_product / (norm1 * norm2))


# Document store interface for non-vector operations

class DocumentStore:
    """Interface for document storage operations."""
    
    def __init__(self, vector_store: VectorStore):
        self.vector_store = vector_store
    
    async def list_documents(
        self,
        limit: int = 20,
        offset: int = 0,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[StoredDocument]:
        """List documents with pagination."""
        # This would be implemented based on the specific backend
        # For now, it's a placeholder
        logger.warning("Document listing not fully implemented")
        return []
    
    async def search_documents(
        self,
        query: str,
        limit: int = 10,
        search_content: bool = True,
        search_metadata: bool = True
    ) -> List[StoredDocument]:
        """Search documents by text query."""
        # This would implement full-text search
        # For now, it's a placeholder
        logger.warning("Document search not fully implemented")
        return []
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get storage statistics."""
        # This would return document count, chunk count, etc.
        return {
            "document_count": 0,
            "chunk_count": 0,
            "total_size_bytes": 0
        }