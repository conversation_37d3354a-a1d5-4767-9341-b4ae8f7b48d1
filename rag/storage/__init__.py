"""
Vector storage implementations for RAG.

This module provides abstractions and implementations for vector storage,
supporting multiple backends like Supabase, PostgreSQL with pgvector, Qdrant, and more.
"""

from .base import (
    StoredDocument,
    StoredChunk,
    SearchFilter,
    VectorStore,
    BaseVectorStore,
    DocumentStore,
)

from .supabase import SupabaseVectorStore
from .pgvector import PgVectorStore
from .qdrant import QdrantVectorStore

__all__ = [
    # Base classes and types
    "StoredDocument",
    "StoredChunk",
    "SearchFilter",
    "VectorStore",
    "BaseVectorStore",
    "DocumentStore",
    
    # Implementations
    "SupabaseVectorStore",
    "PgVectorStore",
    "QdrantVectorStore",
]