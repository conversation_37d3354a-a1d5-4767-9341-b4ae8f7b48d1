"""
PostgreSQL with pgvector implementation.

This module provides a vector storage backend using PostgreSQL with the pgvector extension.
"""

import logging
import json
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import asyncio

import asyncpg

from .base import BaseVectorStore, StoredDocument, StoredChunk, SearchFilter

logger = logging.getLogger(__name__)


class PgVectorStore(BaseVectorStore):
    """PostgreSQL with pgvector implementation."""
    
    def __init__(
        self,
        connection_string: str,
        embedding_dimension: int = 1536,
        documents_table: str = "documents",
        chunks_table: str = "document_chunks"
    ):
        self.connection_string = connection_string
        self.embedding_dimension = embedding_dimension
        self.documents_table = documents_table
        self.chunks_table = chunks_table
        self.pool: Optional[asyncpg.Pool] = None
    
    async def initialize(self) -> None:
        """Initialize connection pool and ensure tables exist."""
        self.pool = await asyncpg.create_pool(self.connection_string)
        
        # Create tables if they don't exist
        async with self.pool.acquire() as conn:
            await self._create_tables(conn)
        
        logger.info("Initialized PostgreSQL vector store")
    
    async def _create_tables(self, conn: asyncpg.Connection):
        """Create necessary tables."""
        # Enable pgvector extension
        await conn.execute("CREATE EXTENSION IF NOT EXISTS vector")
        
        # Create documents table
        await conn.execute(f"""
            CREATE TABLE IF NOT EXISTS {self.documents_table} (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                source TEXT NOT NULL,
                metadata JSONB DEFAULT '{{}}',
                created_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW()
            )
        """)
        
        # Create chunks table with vector column
        await conn.execute(f"""
            CREATE TABLE IF NOT EXISTS {self.chunks_table} (
                id TEXT PRIMARY KEY,
                document_id TEXT REFERENCES {self.documents_table}(id) ON DELETE CASCADE,
                chunk_index INTEGER NOT NULL,
                content TEXT NOT NULL,
                embedding vector({self.embedding_dimension}),
                metadata JSONB DEFAULT '{{}}',
                created_at TIMESTAMPTZ DEFAULT NOW()
            )
        """)
        
        # Create indexes
        await conn.execute(f"""
            CREATE INDEX IF NOT EXISTS idx_{self.chunks_table}_document_id 
            ON {self.chunks_table}(document_id)
        """)
        
        await conn.execute(f"""
            CREATE INDEX IF NOT EXISTS idx_{self.chunks_table}_embedding 
            ON {self.chunks_table} 
            USING ivfflat (embedding vector_cosine_ops)
            WITH (lists = 100)
        """)
    
    async def store_document(
        self,
        document_id: str,
        title: str,
        content: str,
        source: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> StoredDocument:
        """Store a document."""
        if not self.pool:
            raise RuntimeError("Store not initialized")
        
        async with self.pool.acquire() as conn:
            await conn.execute(f"""
                INSERT INTO {self.documents_table} 
                (id, title, content, source, metadata)
                VALUES ($1, $2, $3, $4, $5)
                ON CONFLICT (id) DO UPDATE
                SET title = $2, content = $3, source = $4, 
                    metadata = $5, updated_at = NOW()
            """, document_id, title, content, source, json.dumps(metadata or {}))
        
        return StoredDocument(
            id=document_id,
            title=title,
            content=content,
            source=source,
            metadata=metadata or {},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    async def store_chunk(
        self,
        chunk_id: str,
        document_id: str,
        chunk_index: int,
        content: str,
        embedding: Optional[List[float]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> StoredChunk:
        """Store a chunk."""
        if not self.pool:
            raise RuntimeError("Store not initialized")
        
        # Validate embedding
        embedding = self._validate_embedding(embedding, self.embedding_dimension)
        
        async with self.pool.acquire() as conn:
            if embedding:
                await conn.execute(f"""
                    INSERT INTO {self.chunks_table} 
                    (id, document_id, chunk_index, content, embedding, metadata)
                    VALUES ($1, $2, $3, $4, $5, $6)
                """, chunk_id, document_id, chunk_index, content, embedding, json.dumps(metadata or {}))
            else:
                await conn.execute(f"""
                    INSERT INTO {self.chunks_table} 
                    (id, document_id, chunk_index, content, metadata)
                    VALUES ($1, $2, $3, $4, $5)
                """, chunk_id, document_id, chunk_index, content, json.dumps(metadata or {}))
        
        return StoredChunk(
            id=chunk_id,
            document_id=document_id,
            chunk_index=chunk_index,
            content=content,
            embedding=embedding,
            metadata=metadata or {},
            created_at=datetime.now()
        )
    
    async def search_by_vector(
        self,
        query_embedding: List[float],
        limit: int = 10,
        filters: Optional[List[SearchFilter]] = None
    ) -> List[Tuple[StoredChunk, float]]:
        """Search chunks by vector similarity."""
        if not self.pool:
            raise RuntimeError("Store not initialized")
        
        # Validate query embedding
        query_embedding = self._validate_embedding(query_embedding, self.embedding_dimension)
        
        # Build query with filters
        query = f"""
            SELECT 
                c.*,
                c.embedding <=> $1 as distance,
                1 - (c.embedding <=> $1) as similarity
            FROM {self.chunks_table} c
            JOIN {self.documents_table} d ON c.document_id = d.id
            WHERE c.embedding IS NOT NULL
        """
        
        params = [query_embedding]
        param_count = 1
        
        # Add filters
        if filters:
            for filter in filters:
                param_count += 1
                if filter.field.startswith("metadata."):
                    # JSON field filter
                    field_path = filter.field.replace("metadata.", "")
                    query += f" AND c.metadata->>${param_count} = ${param_count + 1}"
                    params.extend([field_path, str(filter.value)])
                    param_count += 1
                else:
                    # Regular field filter
                    query += f" AND c.{filter.field} = ${param_count}"
                    params.append(filter.value)
        
        query += f" ORDER BY distance LIMIT ${param_count + 1}"
        params.append(limit)
        
        async with self.pool.acquire() as conn:
            rows = await conn.fetch(query, *params)
        
        chunks_with_scores = []
        for row in rows:
            chunk = StoredChunk(
                id=row["id"],
                document_id=row["document_id"],
                chunk_index=row["chunk_index"],
                content=row["content"],
                embedding=row["embedding"],
                metadata=json.loads(row["metadata"]),
                created_at=row["created_at"]
            )
            similarity = row["similarity"]
            chunks_with_scores.append((chunk, similarity))
        
        return chunks_with_scores
    
    async def get_document(self, document_id: str) -> Optional[StoredDocument]:
        """Get document by ID."""
        if not self.pool:
            raise RuntimeError("Store not initialized")
        
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow(
                f"SELECT * FROM {self.documents_table} WHERE id = $1",
                document_id
            )
        
        if row:
            return StoredDocument(
                id=row["id"],
                title=row["title"],
                content=row["content"],
                source=row["source"],
                metadata=json.loads(row["metadata"]),
                created_at=row["created_at"],
                updated_at=row["updated_at"]
            )
        
        return None
    
    async def get_chunks(self, document_id: str) -> List[StoredChunk]:
        """Get all chunks for a document."""
        if not self.pool:
            raise RuntimeError("Store not initialized")
        
        async with self.pool.acquire() as conn:
            rows = await conn.fetch(
                f"SELECT * FROM {self.chunks_table} WHERE document_id = $1 ORDER BY chunk_index",
                document_id
            )
        
        chunks = []
        for row in rows:
            chunk = StoredChunk(
                id=row["id"],
                document_id=row["document_id"],
                chunk_index=row["chunk_index"],
                content=row["content"],
                embedding=row["embedding"],
                metadata=json.loads(row["metadata"]),
                created_at=row["created_at"]
            )
            chunks.append(chunk)
        
        return chunks
    
    async def delete_document(self, document_id: str) -> bool:
        """Delete a document and its chunks."""
        if not self.pool:
            raise RuntimeError("Store not initialized")
        
        try:
            async with self.pool.acquire() as conn:
                # Cascading delete will remove chunks
                result = await conn.execute(
                    f"DELETE FROM {self.documents_table} WHERE id = $1",
                    document_id
                )
                
                return result.split()[-1] != "0"
                
        except Exception as e:
            logger.error(f"Delete error: {str(e)}")
            return False
    
    async def close(self):
        """Close the connection pool."""
        if self.pool:
            await self.pool.close()