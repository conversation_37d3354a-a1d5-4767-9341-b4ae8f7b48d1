"""
Anthropic provider implementation.

This module provides LLM provider implementation for Anthropic's Claude models.
Note: Anthropic does not provide embedding models.
"""

import os
import logging
from typing import List, Dict, Any, Optional, AsyncIterator
import json

import anthropic
from anthropic import AsyncAnthropic
from tenacity import retry, stop_after_attempt, wait_exponential

from .base import (
    BaseLL<PERSON>rovider, ProviderType, ModelCapability,
    ModelInfo, CompletionRequest, ChatRequest,
    CompletionResponse, ChatResponse,
    ChatMessage, StreamChunk, provider_registry
)

logger = logging.getLogger(__name__)


# Model information registry
ANTHROPIC_MODELS = {
    # Claude 3 models
    "claude-3-opus-20240229": ModelInfo(
        name="claude-3-opus-20240229",
        provider=ProviderType.ANTHROPIC,
        capabilities=[
            ModelCapability.CHAT,
            ModelCapability.STREAMING,
            ModelCapability.VISION,
        ],
        context_window=200000,
        max_output_tokens=4096,
        cost_per_1k_input=0.015,
        cost_per_1k_output=0.075,
    ),
    "claude-3-sonnet-20240229": ModelInfo(
        name="claude-3-sonnet-20240229",
        provider=ProviderType.ANTHROPIC,
        capabilities=[
            ModelCapability.CHAT,
            ModelCapability.STREAMING,
            ModelCapability.VISION,
        ],
        context_window=200000,
        max_output_tokens=4096,
        cost_per_1k_input=0.003,
        cost_per_1k_output=0.015,
    ),
    "claude-3-haiku-20240307": ModelInfo(
        name="claude-3-haiku-20240307",
        provider=ProviderType.ANTHROPIC,
        capabilities=[
            ModelCapability.CHAT,
            ModelCapability.STREAMING,
            ModelCapability.VISION,
        ],
        context_window=200000,
        max_output_tokens=4096,
        cost_per_1k_input=0.00025,
        cost_per_1k_output=0.00125,
    ),
    
    # Claude 2 models
    "claude-2.1": ModelInfo(
        name="claude-2.1",
        provider=ProviderType.ANTHROPIC,
        capabilities=[
            ModelCapability.CHAT,
            ModelCapability.STREAMING,
        ],
        context_window=200000,
        max_output_tokens=4096,
        cost_per_1k_input=0.008,
        cost_per_1k_output=0.024,
    ),
    "claude-2.0": ModelInfo(
        name="claude-2.0",
        provider=ProviderType.ANTHROPIC,
        capabilities=[
            ModelCapability.CHAT,
            ModelCapability.STREAMING,
        ],
        context_window=100000,
        max_output_tokens=4096,
        cost_per_1k_input=0.008,
        cost_per_1k_output=0.024,
    ),
    
    # Claude Instant
    "claude-instant-1.2": ModelInfo(
        name="claude-instant-1.2",
        provider=ProviderType.ANTHROPIC,
        capabilities=[
            ModelCapability.CHAT,
            ModelCapability.STREAMING,
        ],
        context_window=100000,
        max_output_tokens=4096,
        cost_per_1k_input=0.0008,
        cost_per_1k_output=0.0024,
    ),
}


class AnthropicLLMProvider(BaseLLMProvider):
    """Anthropic LLM provider implementation."""
    
    def __init__(
        self, 
        model: str = "claude-3-sonnet-20240229",
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        timeout: int = 60,
        max_retries: int = 3,
        **kwargs
    ):
        # Get API key from environment if not provided
        api_key = api_key or os.getenv("ANTHROPIC_API_KEY") or os.getenv("LLM_API_KEY")
        if not api_key:
            raise ValueError("Anthropic API key required")
        
        super().__init__(model=model, api_key=api_key, **kwargs)
        
        # Initialize Anthropic client
        self.client = AsyncAnthropic(
            api_key=api_key,
            base_url=base_url,
            timeout=timeout,
            max_retries=max_retries,
        )
        
        self.timeout = timeout
        self.max_retries = max_retries
    
    @property
    def provider_type(self) -> ProviderType:
        """Get the provider type."""
        return ProviderType.ANTHROPIC
    
    @property
    def model_info(self) -> ModelInfo:
        """Get information about the current model."""
        if self.model in ANTHROPIC_MODELS:
            return ANTHROPIC_MODELS[self.model]
        
        # Return generic info for unknown models
        return ModelInfo(
            name=self.model,
            provider=ProviderType.ANTHROPIC,
            capabilities=[
                ModelCapability.CHAT,
                ModelCapability.STREAMING,
            ],
            context_window=100000,
            max_output_tokens=4096,
        )
    
    def _convert_messages(self, messages: List[ChatMessage]) -> tuple[Optional[str], List[Dict[str, str]]]:
        """Convert messages to Anthropic format, extracting system message."""
        system_message = None
        anthropic_messages = []
        
        for msg in messages:
            if msg.role == "system":
                # Anthropic uses a separate system parameter
                system_message = msg.content
            elif msg.role == "user":
                anthropic_messages.append({
                    "role": "user",
                    "content": msg.content
                })
            elif msg.role == "assistant":
                anthropic_messages.append({
                    "role": "assistant",
                    "content": msg.content
                })
            # Note: Anthropic doesn't support function messages directly
        
        return system_message, anthropic_messages
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def complete(self, request: CompletionRequest) -> CompletionResponse:
        """Generate a completion using the messages endpoint."""
        # Validate request
        self.validate_request(request)
        
        # Convert to chat format
        messages = [ChatMessage(role="user", content=request.prompt)]
        chat_request = ChatRequest(
            messages=messages,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            top_p=request.top_p,
            stop=request.stop,
            seed=request.seed,
        )
        
        response = await self.chat(chat_request)
        
        return CompletionResponse(
            text=response.message.content,
            finish_reason=response.finish_reason,
            usage=response.usage,
            model=response.model,
        )
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def chat(self, request: ChatRequest) -> ChatResponse:
        """Generate a chat response."""
        # Validate request
        self.validate_request(request)
        
        try:
            # Convert messages
            system_message, messages = self._convert_messages(request.messages)
            
            # Prepare kwargs
            kwargs = {
                "model": self.model,
                "messages": messages,
                "max_tokens": request.max_tokens or 4096,  # Anthropic requires max_tokens
                "temperature": request.temperature,
            }
            
            if system_message:
                kwargs["system"] = system_message
            if request.top_p != 1.0:
                kwargs["top_p"] = request.top_p
            if request.stop:
                kwargs["stop_sequences"] = request.stop
            
            # Make API call
            response = await self.client.messages.create(**kwargs)
            
            # Extract response
            content = response.content[0].text if response.content else ""
            
            message = ChatMessage(
                role="assistant",
                content=content,
            )
            
            return ChatResponse(
                message=message,
                finish_reason=response.stop_reason,
                usage={
                    "prompt_tokens": response.usage.input_tokens,
                    "completion_tokens": response.usage.output_tokens,
                    "total_tokens": response.usage.input_tokens + response.usage.output_tokens,
                },
                model=response.model,
            )
            
        except Exception as e:
            logger.error(f"Anthropic chat error: {str(e)}")
            raise
    
    async def stream_complete(self, request: CompletionRequest) -> AsyncIterator[StreamChunk]:
        """Stream a completion."""
        # Convert to chat format
        messages = [ChatMessage(role="user", content=request.prompt)]
        chat_request = ChatRequest(
            messages=messages,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            top_p=request.top_p,
            stop=request.stop,
            seed=request.seed,
        )
        
        async for chunk in self.stream_chat(chat_request):
            yield chunk
    
    async def stream_chat(self, request: ChatRequest) -> AsyncIterator[StreamChunk]:
        """Stream a chat response."""
        # Validate request
        self.validate_request(request)
        
        try:
            # Convert messages
            system_message, messages = self._convert_messages(request.messages)
            
            # Prepare kwargs
            kwargs = {
                "model": self.model,
                "messages": messages,
                "max_tokens": request.max_tokens or 4096,
                "temperature": request.temperature,
                "stream": True,
            }
            
            if system_message:
                kwargs["system"] = system_message
            if request.top_p != 1.0:
                kwargs["top_p"] = request.top_p
            if request.stop:
                kwargs["stop_sequences"] = request.stop
            
            # Make streaming API call
            async with self.client.messages.stream(**kwargs) as stream:
                async for event in stream:
                    if event.type == "content_block_delta":
                        yield StreamChunk(
                            delta=event.delta.text,
                            finish_reason=None,
                        )
                    elif event.type == "message_stop":
                        # Get final message for usage info
                        message = await stream.get_final_message()
                        yield StreamChunk(
                            delta="",
                            finish_reason=message.stop_reason,
                            usage={
                                "prompt_tokens": message.usage.input_tokens,
                                "completion_tokens": message.usage.output_tokens,
                                "total_tokens": message.usage.input_tokens + message.usage.output_tokens,
                            }
                        )
                
        except Exception as e:
            logger.error(f"Anthropic stream error: {str(e)}")
            raise
    
    def get_model_string(self) -> str:
        """Get the model string for Pydantic AI."""
        return f"anthropic:{self.model}"


# Helper function to check if Anthropic models support tools
def convert_tools_to_anthropic_format(tools: List[Dict[str, Any]]) -> Optional[List[Dict[str, Any]]]:
    """
    Convert OpenAI-style tools to Anthropic format (when supported).
    
    Note: As of now, Anthropic doesn't have native tool/function calling,
    but this is here for future compatibility.
    """
    # Placeholder for future implementation
    logger.warning("Anthropic does not currently support native tool calling")
    return None


# Register provider
provider_registry.register_llm_provider("anthropic", AnthropicLLMProvider)