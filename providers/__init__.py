"""
Provider module - LLM and embedding provider implementations.

This module provides a flexible abstraction layer for different AI providers,
allowing easy switching between OpenAI, Anthropic, and local models.
"""

import os
from typing import Optional, Union

from .base import (
    # Types and enums
    ProviderType,
    ModelCapability,
    ModelInfo,
    
    # Request/Response models
    CompletionRequest,
    ChatRequest,
    EmbeddingRequest,
    CompletionResponse,
    ChatResponse,
    EmbeddingResponse,
    ChatMessage,
    StreamChunk,
    
    # Protocols and base classes
    LLMProvider,
    EmbeddingProvider,
    BaseLLMProvider,
    BaseEmbeddingProvider,
    
    # Registry
    ProviderRegistry,
    provider_registry,
)

from .openai import (
    OpenAILLMProvider,
    OpenAIEmbeddingProvider,
    OPENAI_MODELS,
)

from .anthropic import (
    Anthrop<PERSON><PERSON><PERSON>rovider,
    ANTHROPIC_MODELS,
)

from .local import (
    OllamaLLMProvider,
    OllamaEmbeddingProvider,
    LOCAL_MODELS,
)

from .gemini import (
    Gemini<PERSON><PERSON>rovider,
    GeminiEmbeddingProvider,
)


# Convenience functions for creating providers

def get_llm_provider(
    provider: Optional[str] = None,
    model: Optional[str] = None,
    api_key: Optional[str] = None,
    **kwargs
) -> LLMProvider:
    """
    Get an LLM provider instance based on configuration.
    
    Args:
        provider: Provider name (openai, anthropic, local/ollama)
        model: Model name
        api_key: API key (if required)
        **kwargs: Additional provider-specific configuration
    
    Returns:
        LLM provider instance
    """
    # Get from environment if not provided
    provider = provider or os.getenv("LLM_PROVIDER", "openai")
    model = model or os.getenv("LLM_MODEL", "gpt-4-turbo-preview")
    
    # Get API key from environment if not provided
    if not api_key:
        if provider == "openai":
            api_key = os.getenv("OPENAI_API_KEY") or os.getenv("LLM_API_KEY")
        elif provider == "anthropic":
            api_key = os.getenv("ANTHROPIC_API_KEY") or os.getenv("LLM_API_KEY")
        elif provider == "gemini":
            api_key = os.getenv("GEMINI_API_KEY") or os.getenv("LLM_API_KEY")
    
    # Get additional configuration from environment
    env_config = {
        "base_url": os.getenv("LLM_BASE_URL"),
        "timeout": int(os.getenv("LLM_TIMEOUT", "60")),
        "max_retries": int(os.getenv("LLM_MAX_RETRIES", "3")),
    }
    
    # Merge with provided kwargs
    config = {k: v for k, v in env_config.items() if v is not None}
    config.update(kwargs)
    
    return provider_registry.get_llm_provider(
        provider_type=provider,
        model=model,
        api_key=api_key,
        **config
    )


def get_embedding_provider(
    provider: Optional[str] = None,
    model: Optional[str] = None,
    api_key: Optional[str] = None,
    **kwargs
) -> EmbeddingProvider:
    """
    Get an embedding provider instance based on configuration.
    
    Args:
        provider: Provider name (openai, local/ollama)
        model: Model name
        api_key: API key (if required)
        **kwargs: Additional provider-specific configuration
    
    Returns:
        Embedding provider instance
    """
    # Get from environment if not provided
    provider = provider or os.getenv("EMBEDDING_PROVIDER", "openai")
    model = model or os.getenv("EMBEDDING_MODEL", "text-embedding-3-small")
    
    # Get API key from environment if not provided
    if not api_key:
        if provider == "openai":
            api_key = (
                os.getenv("OPENAI_API_KEY") or 
                os.getenv("EMBEDDING_API_KEY") or 
                os.getenv("LLM_API_KEY")
            )
        elif provider == "gemini":
            api_key = (
                os.getenv("GEMINI_API_KEY") or
                os.getenv("EMBEDDING_API_KEY") or
                os.getenv("LLM_API_KEY")
            )
    
    # Get additional configuration from environment
    env_config = {
        "base_url": os.getenv("EMBEDDING_BASE_URL"),
        "timeout": int(os.getenv("EMBEDDING_TIMEOUT", "30")),
        "max_retries": int(os.getenv("EMBEDDING_MAX_RETRIES", "3")),
        "batch_size": int(os.getenv("EMBEDDING_BATCH_SIZE", "100")),
    }
    
    # Special handling for Gemini dimensions
    if provider == "gemini":
        dimensions = os.getenv("GEMINI_EMBEDDING_DIMENSIONS")
        if dimensions:
            env_config["dimensions"] = int(dimensions)
    
    # Merge with provided kwargs
    config = {k: v for k, v in env_config.items() if v is not None}
    config.update(kwargs)
    
    return provider_registry.get_embedding_provider(
        provider_type=provider,
        model=model,
        api_key=api_key,
        **config
    )


# Model catalog functions

def list_available_models(provider: Optional[str] = None) -> dict:
    """
    List available models for each provider.
    
    Args:
        provider: Optional provider filter
    
    Returns:
        Dictionary of provider -> model info
    """
    catalog = {}
    
    if not provider or provider == "openai":
        catalog["openai"] = {
            "llm": [m for m in OPENAI_MODELS.keys() if "embedding" not in m],
            "embedding": [m for m in OPENAI_MODELS.keys() if "embedding" in m],
        }
    
    if not provider or provider == "anthropic":
        catalog["anthropic"] = {
            "llm": list(ANTHROPIC_MODELS.keys()),
            "embedding": [],  # Anthropic doesn't provide embeddings
        }
    
    if not provider or provider in ["local", "ollama"]:
        catalog["local"] = {
            "llm": [m for m in LOCAL_MODELS.keys() if "embed" not in m],
            "embedding": [m for m in LOCAL_MODELS.keys() if "embed" in m],
        }
    
    return catalog


def get_model_info(model: str, provider: Optional[str] = None) -> Optional[ModelInfo]:
    """
    Get information about a specific model.
    
    Args:
        model: Model name
        provider: Optional provider hint
    
    Returns:
        Model information or None if not found
    """
    # Check each provider's models
    if model in OPENAI_MODELS:
        return OPENAI_MODELS[model]
    elif model in ANTHROPIC_MODELS:
        return ANTHROPIC_MODELS[model]
    elif model in LOCAL_MODELS:
        return LOCAL_MODELS[model]
    
    return None


__all__ = [
    # Types and enums
    "ProviderType",
    "ModelCapability",
    "ModelInfo",
    
    # Request/Response models
    "CompletionRequest",
    "ChatRequest",
    "EmbeddingRequest",
    "CompletionResponse",
    "ChatResponse",
    "EmbeddingResponse",
    "ChatMessage",
    "StreamChunk",
    
    # Protocols and base classes
    "LLMProvider",
    "EmbeddingProvider",
    "BaseLLMProvider",
    "BaseEmbeddingProvider",
    
    # Registry
    "ProviderRegistry",
    "provider_registry",
    
    # Provider implementations
    "OpenAILLMProvider",
    "OpenAIEmbeddingProvider",
    "AnthropicLLMProvider",
    "GeminiLLMProvider",
    "GeminiEmbeddingProvider",
    "OllamaLLMProvider",
    "OllamaEmbeddingProvider",
    
    # Convenience functions
    "get_llm_provider",
    "get_embedding_provider",
    "list_available_models",
    "get_model_info",
]