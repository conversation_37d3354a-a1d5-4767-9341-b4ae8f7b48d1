"""
Base provider interfaces and protocols.

This module defines the abstract interfaces for LLM and embedding providers,
allowing flexible switching between different AI services.
"""

from abc import ABC, abstractmethod
from typing import (
    List, Dict, Any, Optional, Protocol, AsyncIterator, 
    Literal, Union, runtime_checkable
)
from dataclasses import dataclass
from enum import Enum

from pydantic import BaseModel, Field


class ProviderType(str, Enum):
    """Supported provider types."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GEMINI = "gemini"
    LOCAL = "local"
    CUSTOM = "custom"


class ModelCapability(str, Enum):
    """Model capabilities."""
    COMPLETION = "completion"
    CHAT = "chat"
    EMBEDDING = "embedding"
    FUNCTION_CALLING = "function_calling"
    STREAMING = "streaming"
    VISION = "vision"
    JSON_MODE = "json_mode"


@dataclass
class ModelInfo:
    """Information about a model."""
    name: str
    provider: ProviderType
    capabilities: List[ModelCapability]
    context_window: int
    max_output_tokens: Optional[int] = None
    embedding_dimensions: Optional[int] = None
    cost_per_1k_input: Optional[float] = None
    cost_per_1k_output: Optional[float] = None
    
    def supports(self, capability: ModelCapability) -> bool:
        """Check if model supports a capability."""
        return capability in self.capabilities


class CompletionRequest(BaseModel):
    """Standard completion request format."""
    prompt: str = Field(..., description="Input prompt")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens to generate")
    temperature: float = Field(0.7, ge=0.0, le=2.0, description="Sampling temperature")
    top_p: float = Field(1.0, ge=0.0, le=1.0, description="Top-p sampling")
    frequency_penalty: float = Field(0.0, ge=-2.0, le=2.0, description="Frequency penalty")
    presence_penalty: float = Field(0.0, ge=-2.0, le=2.0, description="Presence penalty")
    stop: Optional[List[str]] = Field(None, description="Stop sequences")
    seed: Optional[int] = Field(None, description="Random seed")
    response_format: Optional[Literal["text", "json"]] = Field("text", description="Response format")
    

class ChatMessage(BaseModel):
    """Chat message format."""
    role: Literal["system", "user", "assistant", "function"] = Field(..., description="Message role")
    content: str = Field(..., description="Message content")
    name: Optional[str] = Field(None, description="Function name (for function messages)")
    function_call: Optional[Dict[str, Any]] = Field(None, description="Function call info")


class ChatRequest(BaseModel):
    """Standard chat request format."""
    messages: List[ChatMessage] = Field(..., description="Conversation messages")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens to generate")
    temperature: float = Field(0.7, ge=0.0, le=2.0, description="Sampling temperature")
    top_p: float = Field(1.0, ge=0.0, le=1.0, description="Top-p sampling")
    frequency_penalty: float = Field(0.0, ge=-2.0, le=2.0, description="Frequency penalty")
    presence_penalty: float = Field(0.0, ge=-2.0, le=2.0, description="Presence penalty")
    stop: Optional[List[str]] = Field(None, description="Stop sequences")
    seed: Optional[int] = Field(None, description="Random seed")
    response_format: Optional[Literal["text", "json"]] = Field("text", description="Response format")
    tools: Optional[List[Dict[str, Any]]] = Field(None, description="Available tools")
    tool_choice: Optional[Union[str, Dict[str, Any]]] = Field(None, description="Tool selection strategy")


class EmbeddingRequest(BaseModel):
    """Standard embedding request format."""
    input: Union[str, List[str]] = Field(..., description="Text(s) to embed")
    model: Optional[str] = Field(None, description="Embedding model to use")
    dimensions: Optional[int] = Field(None, description="Output dimensions (if supported)")


class CompletionResponse(BaseModel):
    """Standard completion response format."""
    text: str = Field(..., description="Generated text")
    finish_reason: Optional[str] = Field(None, description="Reason for completion")
    usage: Dict[str, int] = Field(default_factory=dict, description="Token usage")
    model: Optional[str] = Field(None, description="Model used")


class ChatResponse(BaseModel):
    """Standard chat response format."""
    message: ChatMessage = Field(..., description="Response message")
    finish_reason: Optional[str] = Field(None, description="Reason for completion")
    usage: Dict[str, int] = Field(default_factory=dict, description="Token usage")
    model: Optional[str] = Field(None, description="Model used")


class EmbeddingResponse(BaseModel):
    """Standard embedding response format."""
    embeddings: List[List[float]] = Field(..., description="Generated embeddings")
    usage: Dict[str, int] = Field(default_factory=dict, description="Token usage")
    model: Optional[str] = Field(None, description="Model used")


class StreamChunk(BaseModel):
    """Stream chunk format."""
    delta: str = Field(..., description="Text delta")
    finish_reason: Optional[str] = Field(None, description="Finish reason if complete")
    usage: Optional[Dict[str, int]] = Field(None, description="Final usage if complete")


@runtime_checkable
class LLMProvider(Protocol):
    """Protocol for LLM providers."""
    
    @property
    def provider_type(self) -> ProviderType:
        """Get the provider type."""
        ...
    
    @property
    def model_info(self) -> ModelInfo:
        """Get information about the current model."""
        ...
    
    async def complete(self, request: CompletionRequest) -> CompletionResponse:
        """Generate a completion."""
        ...
    
    async def chat(self, request: ChatRequest) -> ChatResponse:
        """Generate a chat response."""
        ...
    
    async def stream_complete(self, request: CompletionRequest) -> AsyncIterator[StreamChunk]:
        """Stream a completion."""
        ...
    
    async def stream_chat(self, request: ChatRequest) -> AsyncIterator[StreamChunk]:
        """Stream a chat response."""
        ...
    
    def get_model_string(self) -> str:
        """Get the model string for Pydantic AI."""
        ...


@runtime_checkable
class EmbeddingProvider(Protocol):
    """Protocol for embedding providers."""
    
    @property
    def provider_type(self) -> ProviderType:
        """Get the provider type."""
        ...
    
    @property
    def model_info(self) -> ModelInfo:
        """Get information about the current model."""
        ...
    
    async def embed(self, request: EmbeddingRequest) -> EmbeddingResponse:
        """Generate embeddings."""
        ...
    
    async def embed_batch(
        self, 
        texts: List[str], 
        batch_size: int = 100
    ) -> List[List[float]]:
        """Generate embeddings for multiple texts in batches."""
        ...


class BaseLLMProvider(ABC):
    """Abstract base class for LLM providers."""
    
    def __init__(self, model: str, api_key: Optional[str] = None, **kwargs):
        self.model = model
        self.api_key = api_key
        self.config = kwargs
    
    @property
    @abstractmethod
    def provider_type(self) -> ProviderType:
        """Get the provider type."""
        pass
    
    @property
    @abstractmethod
    def model_info(self) -> ModelInfo:
        """Get information about the current model."""
        pass
    
    @abstractmethod
    async def complete(self, request: CompletionRequest) -> CompletionResponse:
        """Generate a completion."""
        pass
    
    @abstractmethod
    async def chat(self, request: ChatRequest) -> ChatResponse:
        """Generate a chat response."""
        pass
    
    async def stream_complete(self, request: CompletionRequest) -> AsyncIterator[StreamChunk]:
        """Stream a completion (default implementation)."""
        # Default: return single chunk with full response
        response = await self.complete(request)
        yield StreamChunk(
            delta=response.text,
            finish_reason=response.finish_reason,
            usage=response.usage
        )
    
    async def stream_chat(self, request: ChatRequest) -> AsyncIterator[StreamChunk]:
        """Stream a chat response (default implementation)."""
        # Default: return single chunk with full response
        response = await self.chat(request)
        yield StreamChunk(
            delta=response.message.content,
            finish_reason=response.finish_reason,
            usage=response.usage
        )
    
    @abstractmethod
    def get_model_string(self) -> str:
        """Get the model string for Pydantic AI."""
        pass
    
    def validate_request(self, request: Union[CompletionRequest, ChatRequest]) -> None:
        """Validate a request against model constraints."""
        if hasattr(request, "max_tokens") and request.max_tokens:
            if self.model_info.max_output_tokens and request.max_tokens > self.model_info.max_output_tokens:
                raise ValueError(
                    f"Requested max_tokens ({request.max_tokens}) exceeds model limit "
                    f"({self.model_info.max_output_tokens})"
                )
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text (rough approximation)."""
        # Rough estimate: ~4 characters per token
        return len(text) // 4


class BaseEmbeddingProvider(ABC):
    """Abstract base class for embedding providers."""
    
    def __init__(self, model: str, api_key: Optional[str] = None, **kwargs):
        self.model = model
        self.api_key = api_key
        self.config = kwargs
    
    @property
    @abstractmethod
    def provider_type(self) -> ProviderType:
        """Get the provider type."""
        pass
    
    @property
    @abstractmethod
    def model_info(self) -> ModelInfo:
        """Get information about the current model."""
        pass
    
    @abstractmethod
    async def embed(self, request: EmbeddingRequest) -> EmbeddingResponse:
        """Generate embeddings."""
        pass
    
    async def embed_batch(
        self, 
        texts: List[str], 
        batch_size: int = 100
    ) -> List[List[float]]:
        """Generate embeddings for multiple texts in batches."""
        all_embeddings = []
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            request = EmbeddingRequest(input=batch)
            response = await self.embed(request)
            all_embeddings.extend(response.embeddings)
        
        return all_embeddings


# Provider registry for managing available providers

class ProviderRegistry:
    """Registry for managing LLM and embedding providers."""
    
    def __init__(self):
        self.llm_providers: Dict[str, type] = {}
        self.embedding_providers: Dict[str, type] = {}
        self.provider_configs: Dict[str, Dict[str, Any]] = {}
    
    def register_llm_provider(
        self, 
        name: str, 
        provider_class: type,
        config: Optional[Dict[str, Any]] = None
    ):
        """Register an LLM provider."""
        self.llm_providers[name] = provider_class
        if config:
            self.provider_configs[f"llm:{name}"] = config
    
    def register_embedding_provider(
        self, 
        name: str, 
        provider_class: type,
        config: Optional[Dict[str, Any]] = None
    ):
        """Register an embedding provider."""
        self.embedding_providers[name] = provider_class
        if config:
            self.provider_configs[f"embedding:{name}"] = config
    
    def get_llm_provider(
        self, 
        provider_type: str,
        model: str,
        api_key: Optional[str] = None,
        **kwargs
    ) -> LLMProvider:
        """Get an LLM provider instance."""
        if provider_type not in self.llm_providers:
            raise ValueError(f"Unknown LLM provider: {provider_type}")
        
        # Merge stored config with provided kwargs
        config_key = f"llm:{provider_type}"
        config = self.provider_configs.get(config_key, {}).copy()
        config.update(kwargs)
        
        provider_class = self.llm_providers[provider_type]
        return provider_class(model=model, api_key=api_key, **config)
    
    def get_embedding_provider(
        self, 
        provider_type: str,
        model: str,
        api_key: Optional[str] = None,
        **kwargs
    ) -> EmbeddingProvider:
        """Get an embedding provider instance."""
        if provider_type not in self.embedding_providers:
            raise ValueError(f"Unknown embedding provider: {provider_type}")
        
        # Merge stored config with provided kwargs
        config_key = f"embedding:{provider_type}"
        config = self.provider_configs.get(config_key, {}).copy()
        config.update(kwargs)
        
        provider_class = self.embedding_providers[provider_type]
        return provider_class(model=model, api_key=api_key, **config)
    
    def list_llm_providers(self) -> List[str]:
        """List available LLM providers."""
        return list(self.llm_providers.keys())
    
    def list_embedding_providers(self) -> List[str]:
        """List available embedding providers."""
        return list(self.embedding_providers.keys())


# Global provider registry
provider_registry = ProviderRegistry()