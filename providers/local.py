"""
Local model provider implementation.

This module provides support for local models via Ollama and other local inference servers.
Supports both LLM and embedding models running locally.
"""

import os
import logging
from typing import List, Dict, Any, Optional, AsyncIterator
import httpx
import json
from datetime import datetime

from tenacity import retry, stop_after_attempt, wait_exponential

from .base import (
    Base<PERSON><PERSON>rovider, BaseEmbeddingProvider, ProviderType, ModelCapability,
    ModelInfo, CompletionRequest, ChatRequest, EmbeddingRequest,
    CompletionResponse, ChatResponse, EmbeddingResponse,
    ChatMessage, StreamChunk, provider_registry
)

logger = logging.getLogger(__name__)


# Common local models
LOCAL_MODELS = {
    # Llama models
    "llama2": ModelInfo(
        name="llama2",
        provider=ProviderType.LOCAL,
        capabilities=[
            ModelCapability.CHAT,
            ModelCapability.COMPLETION,
            ModelCapability.STREAMING,
        ],
        context_window=4096,
        max_output_tokens=4096,
    ),
    "llama2:13b": ModelInfo(
        name="llama2:13b",
        provider=ProviderType.LOCAL,
        capabilities=[
            ModelCapability.CHAT,
            ModelCapability.COMPLETION,
            ModelCapability.STREAMING,
        ],
        context_window=4096,
        max_output_tokens=4096,
    ),
    "llama2:70b": ModelInfo(
        name="llama2:70b",
        provider=ProviderType.LOCAL,
        capabilities=[
            ModelCapability.CHAT,
            ModelCapability.COMPLETION,
            ModelCapability.STREAMING,
        ],
        context_window=4096,
        max_output_tokens=4096,
    ),
    
    # Mistral models
    "mistral": ModelInfo(
        name="mistral",
        provider=ProviderType.LOCAL,
        capabilities=[
            ModelCapability.CHAT,
            ModelCapability.COMPLETION,
            ModelCapability.STREAMING,
        ],
        context_window=8192,
        max_output_tokens=8192,
    ),
    "mixtral": ModelInfo(
        name="mixtral",
        provider=ProviderType.LOCAL,
        capabilities=[
            ModelCapability.CHAT,
            ModelCapability.COMPLETION,
            ModelCapability.STREAMING,
        ],
        context_window=32768,
        max_output_tokens=32768,
    ),
    
    # Code models
    "codellama": ModelInfo(
        name="codellama",
        provider=ProviderType.LOCAL,
        capabilities=[
            ModelCapability.CHAT,
            ModelCapability.COMPLETION,
            ModelCapability.STREAMING,
        ],
        context_window=16384,
        max_output_tokens=16384,
    ),
    
    # Embedding models
    "nomic-embed-text": ModelInfo(
        name="nomic-embed-text",
        provider=ProviderType.LOCAL,
        capabilities=[ModelCapability.EMBEDDING],
        context_window=8192,
        embedding_dimensions=768,
    ),
    "all-minilm": ModelInfo(
        name="all-minilm",
        provider=ProviderType.LOCAL,
        capabilities=[ModelCapability.EMBEDDING],
        context_window=256,
        embedding_dimensions=384,
    ),
}


class OllamaLLMProvider(BaseLLMProvider):
    """Ollama local LLM provider implementation."""
    
    def __init__(
        self, 
        model: str = "llama2",
        api_key: Optional[str] = None,  # Not used but kept for interface compatibility
        base_url: str = "http://localhost:11434",
        timeout: int = 120,  # Longer timeout for local models
        max_retries: int = 3,
        **kwargs
    ):
        super().__init__(model=model, api_key=api_key, **kwargs)
        
        self.base_url = base_url.rstrip("/")
        self.timeout = timeout
        self.max_retries = max_retries
        
        # Create HTTP client
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=httpx.Timeout(timeout),
        )
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.client.aclose()
    
    @property
    def provider_type(self) -> ProviderType:
        """Get the provider type."""
        return ProviderType.LOCAL
    
    @property
    def model_info(self) -> ModelInfo:
        """Get information about the current model."""
        if self.model in LOCAL_MODELS:
            return LOCAL_MODELS[self.model]
        
        # Return generic info for unknown models
        return ModelInfo(
            name=self.model,
            provider=ProviderType.LOCAL,
            capabilities=[
                ModelCapability.CHAT,
                ModelCapability.COMPLETION,
                ModelCapability.STREAMING,
            ],
            context_window=4096,
            max_output_tokens=4096,
        )
    
    async def _check_model_exists(self) -> bool:
        """Check if the model exists locally."""
        try:
            response = await self.client.get("/api/tags")
            if response.status_code == 200:
                data = response.json()
                models = [m["name"] for m in data.get("models", [])]
                return self.model in models
            return False
        except Exception:
            return False
    
    async def _pull_model(self):
        """Pull the model if it doesn't exist."""
        logger.info(f"Pulling model {self.model}...")
        try:
            response = await self.client.post(
                "/api/pull",
                json={"name": self.model},
                timeout=600  # 10 minute timeout for pulling
            )
            if response.status_code == 200:
                logger.info(f"Successfully pulled model {self.model}")
            else:
                logger.error(f"Failed to pull model: {response.text}")
        except Exception as e:
            logger.error(f"Error pulling model: {str(e)}")
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def complete(self, request: CompletionRequest) -> CompletionResponse:
        """Generate a completion."""
        # Check if model exists
        if not await self._check_model_exists():
            await self._pull_model()
        
        try:
            response = await self.client.post(
                "/api/generate",
                json={
                    "model": self.model,
                    "prompt": request.prompt,
                    "stream": False,
                    "options": {
                        "temperature": request.temperature,
                        "top_p": request.top_p,
                        "stop": request.stop,
                        "seed": request.seed,
                        "num_predict": request.max_tokens,
                    }
                }
            )
            
            if response.status_code != 200:
                raise Exception(f"Ollama API error: {response.text}")
            
            data = response.json()
            
            return CompletionResponse(
                text=data["response"],
                finish_reason="stop",
                usage={
                    "prompt_tokens": data.get("prompt_eval_count", 0),
                    "completion_tokens": data.get("eval_count", 0),
                    "total_tokens": data.get("prompt_eval_count", 0) + data.get("eval_count", 0),
                },
                model=self.model,
            )
            
        except Exception as e:
            logger.error(f"Ollama completion error: {str(e)}")
            raise
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def chat(self, request: ChatRequest) -> ChatResponse:
        """Generate a chat response."""
        # Check if model exists
        if not await self._check_model_exists():
            await self._pull_model()
        
        try:
            # Convert messages to Ollama format
            messages = []
            for msg in request.messages:
                messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
            
            response = await self.client.post(
                "/api/chat",
                json={
                    "model": self.model,
                    "messages": messages,
                    "stream": False,
                    "options": {
                        "temperature": request.temperature,
                        "top_p": request.top_p,
                        "stop": request.stop,
                        "seed": request.seed,
                        "num_predict": request.max_tokens,
                    }
                }
            )
            
            if response.status_code != 200:
                raise Exception(f"Ollama API error: {response.text}")
            
            data = response.json()
            
            message = ChatMessage(
                role="assistant",
                content=data["message"]["content"],
            )
            
            return ChatResponse(
                message=message,
                finish_reason="stop",
                usage={
                    "prompt_tokens": data.get("prompt_eval_count", 0),
                    "completion_tokens": data.get("eval_count", 0),
                    "total_tokens": data.get("prompt_eval_count", 0) + data.get("eval_count", 0),
                },
                model=self.model,
            )
            
        except Exception as e:
            logger.error(f"Ollama chat error: {str(e)}")
            raise
    
    async def stream_complete(self, request: CompletionRequest) -> AsyncIterator[StreamChunk]:
        """Stream a completion."""
        # Check if model exists
        if not await self._check_model_exists():
            await self._pull_model()
        
        try:
            async with self.client.stream(
                "POST",
                "/api/generate",
                json={
                    "model": self.model,
                    "prompt": request.prompt,
                    "stream": True,
                    "options": {
                        "temperature": request.temperature,
                        "top_p": request.top_p,
                        "stop": request.stop,
                        "seed": request.seed,
                        "num_predict": request.max_tokens,
                    }
                }
            ) as response:
                async for line in response.aiter_lines():
                    if line:
                        try:
                            data = json.loads(line)
                            if "response" in data:
                                yield StreamChunk(
                                    delta=data["response"],
                                    finish_reason="stop" if data.get("done") else None,
                                )
                            
                            if data.get("done"):
                                yield StreamChunk(
                                    delta="",
                                    finish_reason="stop",
                                    usage={
                                        "prompt_tokens": data.get("prompt_eval_count", 0),
                                        "completion_tokens": data.get("eval_count", 0),
                                        "total_tokens": data.get("prompt_eval_count", 0) + data.get("eval_count", 0),
                                    }
                                )
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error(f"Ollama stream error: {str(e)}")
            raise
    
    async def stream_chat(self, request: ChatRequest) -> AsyncIterator[StreamChunk]:
        """Stream a chat response."""
        # Check if model exists
        if not await self._check_model_exists():
            await self._pull_model()
        
        try:
            # Convert messages to Ollama format
            messages = []
            for msg in request.messages:
                messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
            
            async with self.client.stream(
                "POST",
                "/api/chat",
                json={
                    "model": self.model,
                    "messages": messages,
                    "stream": True,
                    "options": {
                        "temperature": request.temperature,
                        "top_p": request.top_p,
                        "stop": request.stop,
                        "seed": request.seed,
                        "num_predict": request.max_tokens,
                    }
                }
            ) as response:
                async for line in response.aiter_lines():
                    if line:
                        try:
                            data = json.loads(line)
                            if "message" in data and "content" in data["message"]:
                                yield StreamChunk(
                                    delta=data["message"]["content"],
                                    finish_reason="stop" if data.get("done") else None,
                                )
                            
                            if data.get("done"):
                                yield StreamChunk(
                                    delta="",
                                    finish_reason="stop",
                                    usage={
                                        "prompt_tokens": data.get("prompt_eval_count", 0),
                                        "completion_tokens": data.get("eval_count", 0),
                                        "total_tokens": data.get("prompt_eval_count", 0) + data.get("eval_count", 0),
                                    }
                                )
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error(f"Ollama stream chat error: {str(e)}")
            raise
    
    def get_model_string(self) -> str:
        """Get the model string for Pydantic AI."""
        # For Ollama, we'll use a custom format
        return f"ollama:{self.model}"


class OllamaEmbeddingProvider(BaseEmbeddingProvider):
    """Ollama local embedding provider implementation."""
    
    def __init__(
        self, 
        model: str = "nomic-embed-text",
        api_key: Optional[str] = None,
        base_url: str = "http://localhost:11434",
        timeout: int = 60,
        max_retries: int = 3,
        **kwargs
    ):
        super().__init__(model=model, api_key=api_key, **kwargs)
        
        self.base_url = base_url.rstrip("/")
        self.timeout = timeout
        self.max_retries = max_retries
        
        # Create HTTP client
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=httpx.Timeout(timeout),
        )
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.client.aclose()
    
    @property
    def provider_type(self) -> ProviderType:
        """Get the provider type."""
        return ProviderType.LOCAL
    
    @property
    def model_info(self) -> ModelInfo:
        """Get information about the current model."""
        if self.model in LOCAL_MODELS:
            return LOCAL_MODELS[self.model]
        
        # Return generic info for unknown models
        return ModelInfo(
            name=self.model,
            provider=ProviderType.LOCAL,
            capabilities=[ModelCapability.EMBEDDING],
            context_window=8192,
            embedding_dimensions=768,
        )
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def embed(self, request: EmbeddingRequest) -> EmbeddingResponse:
        """Generate embeddings."""
        try:
            # Handle single string or list
            if isinstance(request.input, str):
                input_texts = [request.input]
            else:
                input_texts = request.input
            
            embeddings = []
            total_tokens = 0
            
            # Process each text individually (Ollama doesn't batch)
            for text in input_texts:
                response = await self.client.post(
                    "/api/embeddings",
                    json={
                        "model": request.model or self.model,
                        "prompt": text,
                    }
                )
                
                if response.status_code != 200:
                    raise Exception(f"Ollama API error: {response.text}")
                
                data = response.json()
                embeddings.append(data["embedding"])
                total_tokens += len(text.split())  # Rough estimate
            
            return EmbeddingResponse(
                embeddings=embeddings,
                usage={
                    "prompt_tokens": total_tokens,
                    "total_tokens": total_tokens,
                },
                model=self.model,
            )
            
        except Exception as e:
            logger.error(f"Ollama embedding error: {str(e)}")
            raise


# Register providers
provider_registry.register_llm_provider("ollama", OllamaLLMProvider)
provider_registry.register_llm_provider("local", OllamaLLMProvider)  # Alias
provider_registry.register_embedding_provider("ollama", OllamaEmbeddingProvider)
provider_registry.register_embedding_provider("local", OllamaEmbeddingProvider)  # Alias