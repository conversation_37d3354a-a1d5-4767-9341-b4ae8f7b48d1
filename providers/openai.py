"""
OpenAI provider implementation.

This module provides LLM and embedding provider implementations for OpenAI's API,
including support for GPT models and embedding models.
"""

import os
import logging
from typing import List, Dict, Any, Optional, AsyncIterator
import asyncio

import openai
from openai import AsyncOpenAI
from tenacity import retry, stop_after_attempt, wait_exponential

from .base import (
    Base<PERSON><PERSON>rovider, BaseEmbeddingProvider, ProviderType, ModelCapability,
    ModelInfo, CompletionRequest, ChatRequest, EmbeddingRequest,
    CompletionResponse, ChatResponse, EmbeddingResponse,
    ChatMessage, StreamChunk, provider_registry
)

logger = logging.getLogger(__name__)


# Model information registry
OPENAI_MODELS = {
    # GPT-4 models
    "gpt-4-turbo-preview": ModelInfo(
        name="gpt-4-turbo-preview",
        provider=ProviderType.OPENAI,
        capabilities=[
            ModelCapability.CHAT,
            ModelCapability.FUNCTION_CALLING,
            ModelCapability.STREAMING,
            ModelCapability.VISION,
            ModelCapability.JSON_MODE,
        ],
        context_window=128000,
        max_output_tokens=4096,
        cost_per_1k_input=0.01,
        cost_per_1k_output=0.03,
    ),
    "gpt-4": ModelInfo(
        name="gpt-4",
        provider=ProviderType.OPENAI,
        capabilities=[
            ModelCapability.CHAT,
            ModelCapability.FUNCTION_CALLING,
            ModelCapability.STREAMING,
        ],
        context_window=8192,
        max_output_tokens=4096,
        cost_per_1k_input=0.03,
        cost_per_1k_output=0.06,
    ),
    "gpt-4-32k": ModelInfo(
        name="gpt-4-32k",
        provider=ProviderType.OPENAI,
        capabilities=[
            ModelCapability.CHAT,
            ModelCapability.FUNCTION_CALLING,
            ModelCapability.STREAMING,
        ],
        context_window=32768,
        max_output_tokens=4096,
        cost_per_1k_input=0.06,
        cost_per_1k_output=0.12,
    ),
    
    # GPT-3.5 models
    "gpt-3.5-turbo": ModelInfo(
        name="gpt-3.5-turbo",
        provider=ProviderType.OPENAI,
        capabilities=[
            ModelCapability.CHAT,
            ModelCapability.FUNCTION_CALLING,
            ModelCapability.STREAMING,
            ModelCapability.JSON_MODE,
        ],
        context_window=16384,
        max_output_tokens=4096,
        cost_per_1k_input=0.0005,
        cost_per_1k_output=0.0015,
    ),
    
    # Embedding models
    "text-embedding-3-small": ModelInfo(
        name="text-embedding-3-small",
        provider=ProviderType.OPENAI,
        capabilities=[ModelCapability.EMBEDDING],
        context_window=8191,
        embedding_dimensions=1536,
        cost_per_1k_input=0.00002,
    ),
    "text-embedding-3-large": ModelInfo(
        name="text-embedding-3-large",
        provider=ProviderType.OPENAI,
        capabilities=[ModelCapability.EMBEDDING],
        context_window=8191,
        embedding_dimensions=3072,
        cost_per_1k_input=0.00013,
    ),
    "text-embedding-ada-002": ModelInfo(
        name="text-embedding-ada-002",
        provider=ProviderType.OPENAI,
        capabilities=[ModelCapability.EMBEDDING],
        context_window=8191,
        embedding_dimensions=1536,
        cost_per_1k_input=0.0001,
    ),
}


class OpenAILLMProvider(BaseLLMProvider):
    """OpenAI LLM provider implementation."""
    
    def __init__(
        self, 
        model: str = "gpt-4-turbo-preview",
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        organization: Optional[str] = None,
        timeout: int = 60,
        max_retries: int = 3,
        **kwargs
    ):
        # Get API key from environment if not provided
        api_key = api_key or os.getenv("OPENAI_API_KEY") or os.getenv("LLM_API_KEY")
        if not api_key:
            raise ValueError("OpenAI API key required")
        
        super().__init__(model=model, api_key=api_key, **kwargs)
        
        # Initialize OpenAI client
        self.client = AsyncOpenAI(
            api_key=api_key,
            base_url=base_url,
            organization=organization,
            timeout=timeout,
            max_retries=max_retries,
        )
        
        self.timeout = timeout
        self.max_retries = max_retries
    
    @property
    def provider_type(self) -> ProviderType:
        """Get the provider type."""
        return ProviderType.OPENAI
    
    @property
    def model_info(self) -> ModelInfo:
        """Get information about the current model."""
        if self.model in OPENAI_MODELS:
            return OPENAI_MODELS[self.model]
        
        # Return generic info for unknown models
        return ModelInfo(
            name=self.model,
            provider=ProviderType.OPENAI,
            capabilities=[
                ModelCapability.CHAT,
                ModelCapability.FUNCTION_CALLING,
                ModelCapability.STREAMING,
            ],
            context_window=8192,
            max_output_tokens=4096,
        )
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def complete(self, request: CompletionRequest) -> CompletionResponse:
        """Generate a completion using the chat endpoint."""
        # Validate request
        self.validate_request(request)
        
        # Convert to chat format (OpenAI deprecated completions API)
        messages = [ChatMessage(role="user", content=request.prompt)]
        chat_request = ChatRequest(
            messages=messages,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            top_p=request.top_p,
            frequency_penalty=request.frequency_penalty,
            presence_penalty=request.presence_penalty,
            stop=request.stop,
            seed=request.seed,
            response_format=request.response_format,
        )
        
        response = await self.chat(chat_request)
        
        return CompletionResponse(
            text=response.message.content,
            finish_reason=response.finish_reason,
            usage=response.usage,
            model=response.model,
        )
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def chat(self, request: ChatRequest) -> ChatResponse:
        """Generate a chat response."""
        # Validate request
        self.validate_request(request)
        
        try:
            # Prepare messages
            messages = [
                {"role": msg.role, "content": msg.content}
                for msg in request.messages
            ]
            
            # Prepare kwargs
            kwargs = {
                "model": self.model,
                "messages": messages,
                "temperature": request.temperature,
                "top_p": request.top_p,
                "frequency_penalty": request.frequency_penalty,
                "presence_penalty": request.presence_penalty,
            }
            
            if request.max_tokens:
                kwargs["max_tokens"] = request.max_tokens
            if request.stop:
                kwargs["stop"] = request.stop
            if request.seed is not None:
                kwargs["seed"] = request.seed
            
            # Handle response format
            if request.response_format == "json" and self.model_info.supports(ModelCapability.JSON_MODE):
                kwargs["response_format"] = {"type": "json_object"}
            
            # Handle tools
            if request.tools:
                kwargs["tools"] = request.tools
            if request.tool_choice:
                kwargs["tool_choice"] = request.tool_choice
            
            # Make API call
            response = await self.client.chat.completions.create(**kwargs)
            
            # Extract response
            choice = response.choices[0]
            message = ChatMessage(
                role=choice.message.role,
                content=choice.message.content or "",
            )
            
            # Add function call if present
            if hasattr(choice.message, "tool_calls") and choice.message.tool_calls:
                message.function_call = {
                    "name": choice.message.tool_calls[0].function.name,
                    "arguments": choice.message.tool_calls[0].function.arguments,
                }
            
            return ChatResponse(
                message=message,
                finish_reason=choice.finish_reason,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens,
                },
                model=response.model,
            )
            
        except Exception as e:
            logger.error(f"OpenAI chat error: {str(e)}")
            raise
    
    async def stream_complete(self, request: CompletionRequest) -> AsyncIterator[StreamChunk]:
        """Stream a completion."""
        # Convert to chat format
        messages = [ChatMessage(role="user", content=request.prompt)]
        chat_request = ChatRequest(
            messages=messages,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            top_p=request.top_p,
            frequency_penalty=request.frequency_penalty,
            presence_penalty=request.presence_penalty,
            stop=request.stop,
            seed=request.seed,
            response_format=request.response_format,
        )
        
        async for chunk in self.stream_chat(chat_request):
            yield chunk
    
    async def stream_chat(self, request: ChatRequest) -> AsyncIterator[StreamChunk]:
        """Stream a chat response."""
        # Validate request
        self.validate_request(request)
        
        try:
            # Prepare messages
            messages = [
                {"role": msg.role, "content": msg.content}
                for msg in request.messages
            ]
            
            # Prepare kwargs
            kwargs = {
                "model": self.model,
                "messages": messages,
                "temperature": request.temperature,
                "top_p": request.top_p,
                "frequency_penalty": request.frequency_penalty,
                "presence_penalty": request.presence_penalty,
                "stream": True,
            }
            
            if request.max_tokens:
                kwargs["max_tokens"] = request.max_tokens
            if request.stop:
                kwargs["stop"] = request.stop
            if request.seed is not None:
                kwargs["seed"] = request.seed
            
            # Make streaming API call
            stream = await self.client.chat.completions.create(**kwargs)
            
            accumulated_content = ""
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    delta = chunk.choices[0].delta.content
                    accumulated_content += delta
                    
                    yield StreamChunk(
                        delta=delta,
                        finish_reason=chunk.choices[0].finish_reason,
                    )
            
            # Yield final chunk with usage info if available
            if hasattr(chunk, "usage") and chunk.usage:
                yield StreamChunk(
                    delta="",
                    finish_reason="stop",
                    usage={
                        "prompt_tokens": chunk.usage.prompt_tokens,
                        "completion_tokens": chunk.usage.completion_tokens,
                        "total_tokens": chunk.usage.total_tokens,
                    }
                )
                
        except Exception as e:
            logger.error(f"OpenAI stream error: {str(e)}")
            raise
    
    def get_model_string(self) -> str:
        """Get the model string for Pydantic AI."""
        return f"openai:{self.model}"


class OpenAIEmbeddingProvider(BaseEmbeddingProvider):
    """OpenAI embedding provider implementation."""
    
    def __init__(
        self, 
        model: str = "text-embedding-3-small",
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        organization: Optional[str] = None,
        timeout: int = 30,
        max_retries: int = 3,
        **kwargs
    ):
        # Get API key from environment if not provided
        api_key = api_key or os.getenv("OPENAI_API_KEY") or os.getenv("EMBEDDING_API_KEY") or os.getenv("LLM_API_KEY")
        if not api_key:
            raise ValueError("OpenAI API key required")
        
        super().__init__(model=model, api_key=api_key, **kwargs)
        
        # Initialize OpenAI client
        self.client = AsyncOpenAI(
            api_key=api_key,
            base_url=base_url,
            organization=organization,
            timeout=timeout,
            max_retries=max_retries,
        )
    
    @property
    def provider_type(self) -> ProviderType:
        """Get the provider type."""
        return ProviderType.OPENAI
    
    @property
    def model_info(self) -> ModelInfo:
        """Get information about the current model."""
        if self.model in OPENAI_MODELS:
            return OPENAI_MODELS[self.model]
        
        # Return generic info for unknown models
        return ModelInfo(
            name=self.model,
            provider=ProviderType.OPENAI,
            capabilities=[ModelCapability.EMBEDDING],
            context_window=8191,
            embedding_dimensions=1536,
        )
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def embed(self, request: EmbeddingRequest) -> EmbeddingResponse:
        """Generate embeddings."""
        try:
            # Handle single string or list
            if isinstance(request.input, str):
                input_texts = [request.input]
            else:
                input_texts = request.input
            
            # Prepare kwargs
            kwargs = {
                "model": request.model or self.model,
                "input": input_texts,
            }
            
            # Add dimensions if specified and supported
            if request.dimensions and self.model.startswith("text-embedding-3"):
                kwargs["dimensions"] = request.dimensions
            
            # Make API call
            response = await self.client.embeddings.create(**kwargs)
            
            # Extract embeddings
            embeddings = [item.embedding for item in response.data]
            
            return EmbeddingResponse(
                embeddings=embeddings,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "total_tokens": response.usage.total_tokens,
                },
                model=response.model,
            )
            
        except Exception as e:
            logger.error(f"OpenAI embedding error: {str(e)}")
            raise
    
    async def embed_batch(
        self, 
        texts: List[str], 
        batch_size: int = 100
    ) -> List[List[float]]:
        """Generate embeddings for multiple texts in batches."""
        # OpenAI supports up to 2048 embedding inputs per request
        # But we'll use a smaller default for safety
        batch_size = min(batch_size, 100)
        
        return await super().embed_batch(texts, batch_size)


# Register providers
provider_registry.register_llm_provider("openai", OpenAILLMProvider)
provider_registry.register_embedding_provider("openai", OpenAIEmbeddingProvider)