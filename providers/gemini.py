"""
Google Gemini provider implementation.

This module provides LLM and embedding support for Google's Gemini models,
including the Gemini Pro family and text embedding models.
"""

import os
import logging
from typing import List, Dict, Any, Optional, AsyncIterator
import asyncio
import json

import google.generativeai as genai
from google.generativeai.types import GenerateContentResponse, Content, Part
from google.ai.generativelanguage import Candidate
import httpx
from tenacity import retry, stop_after_attempt, wait_exponential

from .base import (
    BaseLLMProvider, BaseEmbeddingProvider, ProviderType, ModelInfo,
    ModelCapability, CompletionRequest, CompletionResponse,
    ChatRequest, ChatResponse, ChatMessage, EmbeddingRequest,
    EmbeddingResponse, StreamChunk
)

logger = logging.getLogger(__name__)


class GeminiLLMProvider(BaseLLMProvider):
    """Google Gemini LLM provider implementation."""
    
    # Model configurations
    MODEL_CONFIGS = {
        "gemini-pro": {
            "context_window": 32768,
            "max_output_tokens": 8192,
            "cost_per_1k_input": 0.0005,
            "cost_per_1k_output": 0.0015,
        },
        "gemini-pro-vision": {
            "context_window": 16384,
            "max_output_tokens": 2048,
            "cost_per_1k_input": 0.0005,
            "cost_per_1k_output": 0.0015,
        },
        "gemini-1.5-pro": {
            "context_window": 1048576,  # 1M tokens
            "max_output_tokens": 8192,
            "cost_per_1k_input": 0.007,
            "cost_per_1k_output": 0.021,
        },
        "gemini-1.5-flash": {
            "context_window": 1048576,  # 1M tokens
            "max_output_tokens": 8192,
            "cost_per_1k_input": 0.00035,
            "cost_per_1k_output": 0.00105,
        }
    }
    
    def __init__(
        self,
        model: str = "gemini-pro",
        api_key: Optional[str] = None,
        **kwargs
    ):
        super().__init__(model, api_key, **kwargs)
        
        # Initialize Gemini API
        api_key = api_key or os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("Gemini API key is required")
        
        genai.configure(api_key=api_key)
        
        # Initialize the model
        self.client = genai.GenerativeModel(model)
        
        # Safety settings (configurable)
        self.safety_settings = kwargs.get("safety_settings", [
            {
                "category": "HARM_CATEGORY_HARASSMENT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_HATE_SPEECH",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            }
        ])
        
        logger.info(f"Initialized Gemini LLM provider with model: {model}")
    
    @property
    def provider_type(self) -> ProviderType:
        """Get the provider type."""
        return ProviderType.GEMINI
    
    @property
    def model_info(self) -> ModelInfo:
        """Get information about the current model."""
        config = self.MODEL_CONFIGS.get(self.model, self.MODEL_CONFIGS["gemini-pro"])
        
        capabilities = [
            ModelCapability.COMPLETION,
            ModelCapability.CHAT,
            ModelCapability.STREAMING,
            ModelCapability.FUNCTION_CALLING,
        ]
        
        if "vision" in self.model:
            capabilities.append(ModelCapability.VISION)
        
        return ModelInfo(
            name=self.model,
            provider=self.provider_type,
            capabilities=capabilities,
            context_window=config["context_window"],
            max_output_tokens=config["max_output_tokens"],
            cost_per_1k_input=config["cost_per_1k_input"],
            cost_per_1k_output=config["cost_per_1k_output"]
        )
    
    def _convert_chat_messages(self, messages: List[ChatMessage]) -> List[Content]:
        """Convert our chat messages to Gemini format."""
        contents = []
        
        for msg in messages:
            if msg.role == "system":
                # Gemini doesn't have a system role, prepend to first user message
                continue
            
            role = "user" if msg.role == "user" else "model"
            contents.append(Content(role=role, parts=[Part(text=msg.content)]))
        
        # Handle system messages by prepending to first user message
        system_messages = [m for m in messages if m.role == "system"]
        if system_messages and contents:
            system_text = "\n".join(m.content for m in system_messages)
            if contents[0].role == "user":
                original_text = contents[0].parts[0].text
                contents[0].parts[0].text = f"{system_text}\n\n{original_text}"
        
        return contents
    
    def _create_generation_config(
        self,
        request: Union[CompletionRequest, ChatRequest]
    ) -> Dict[str, Any]:
        """Create Gemini generation configuration."""
        config = {
            "temperature": request.temperature,
            "top_p": request.top_p,
            "max_output_tokens": request.max_tokens,
        }
        
        if request.stop:
            config["stop_sequences"] = request.stop
        
        # Remove None values
        return {k: v for k, v in config.items() if v is not None}
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def complete(self, request: CompletionRequest) -> CompletionResponse:
        """Generate a completion."""
        self.validate_request(request)
        
        generation_config = self._create_generation_config(request)
        
        try:
            # Run in executor to avoid blocking
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.client.generate_content(
                    request.prompt,
                    generation_config=generation_config,
                    safety_settings=self.safety_settings
                )
            )
            
            # Extract text from response
            text = response.text if hasattr(response, 'text') else ""
            
            # Get finish reason
            finish_reason = "stop"
            if response.candidates and response.candidates[0].finish_reason:
                finish_reason = response.candidates[0].finish_reason.name.lower()
            
            # Estimate token usage (Gemini doesn't always provide exact counts)
            usage = {
                "prompt_tokens": self.estimate_tokens(request.prompt),
                "completion_tokens": self.estimate_tokens(text),
                "total_tokens": 0
            }
            usage["total_tokens"] = usage["prompt_tokens"] + usage["completion_tokens"]
            
            return CompletionResponse(
                text=text,
                finish_reason=finish_reason,
                usage=usage,
                model=self.model
            )
            
        except Exception as e:
            logger.error(f"Gemini completion error: {str(e)}")
            raise
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def chat(self, request: ChatRequest) -> ChatResponse:
        """Generate a chat response."""
        self.validate_request(request)
        
        contents = self._convert_chat_messages(request.messages)
        generation_config = self._create_generation_config(request)
        
        try:
            # Create chat session
            chat = self.client.start_chat(history=contents[:-1] if len(contents) > 1 else [])
            
            # Send the last message
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: chat.send_message(
                    contents[-1].parts[0].text if contents else "",
                    generation_config=generation_config,
                    safety_settings=self.safety_settings
                )
            )
            
            # Extract response
            text = response.text if hasattr(response, 'text') else ""
            
            # Get finish reason
            finish_reason = "stop"
            if response.candidates and response.candidates[0].finish_reason:
                finish_reason = response.candidates[0].finish_reason.name.lower()
            
            # Estimate token usage
            prompt_text = " ".join(m.content for m in request.messages)
            usage = {
                "prompt_tokens": self.estimate_tokens(prompt_text),
                "completion_tokens": self.estimate_tokens(text),
                "total_tokens": 0
            }
            usage["total_tokens"] = usage["prompt_tokens"] + usage["completion_tokens"]
            
            response_message = ChatMessage(
                role="assistant",
                content=text
            )
            
            return ChatResponse(
                message=response_message,
                finish_reason=finish_reason,
                usage=usage,
                model=self.model
            )
            
        except Exception as e:
            logger.error(f"Gemini chat error: {str(e)}")
            raise
    
    async def stream_complete(self, request: CompletionRequest) -> AsyncIterator[StreamChunk]:
        """Stream a completion."""
        self.validate_request(request)
        
        generation_config = self._create_generation_config(request)
        
        try:
            # Generate content with streaming
            response = self.client.generate_content(
                request.prompt,
                generation_config=generation_config,
                safety_settings=self.safety_settings,
                stream=True
            )
            
            full_text = ""
            for chunk in response:
                if chunk.text:
                    full_text += chunk.text
                    yield StreamChunk(delta=chunk.text)
            
            # Final chunk with usage
            usage = {
                "prompt_tokens": self.estimate_tokens(request.prompt),
                "completion_tokens": self.estimate_tokens(full_text),
                "total_tokens": 0
            }
            usage["total_tokens"] = usage["prompt_tokens"] + usage["completion_tokens"]
            
            yield StreamChunk(
                delta="",
                finish_reason="stop",
                usage=usage
            )
            
        except Exception as e:
            logger.error(f"Gemini streaming error: {str(e)}")
            raise
    
    async def stream_chat(self, request: ChatRequest) -> AsyncIterator[StreamChunk]:
        """Stream a chat response."""
        self.validate_request(request)
        
        contents = self._convert_chat_messages(request.messages)
        generation_config = self._create_generation_config(request)
        
        try:
            # Create chat session
            chat = self.client.start_chat(history=contents[:-1] if len(contents) > 1 else [])
            
            # Send message with streaming
            response = chat.send_message(
                contents[-1].parts[0].text if contents else "",
                generation_config=generation_config,
                safety_settings=self.safety_settings,
                stream=True
            )
            
            full_text = ""
            for chunk in response:
                if chunk.text:
                    full_text += chunk.text
                    yield StreamChunk(delta=chunk.text)
            
            # Final chunk with usage
            prompt_text = " ".join(m.content for m in request.messages)
            usage = {
                "prompt_tokens": self.estimate_tokens(prompt_text),
                "completion_tokens": self.estimate_tokens(full_text),
                "total_tokens": 0
            }
            usage["total_tokens"] = usage["prompt_tokens"] + usage["completion_tokens"]
            
            yield StreamChunk(
                delta="",
                finish_reason="stop",
                usage=usage
            )
            
        except Exception as e:
            logger.error(f"Gemini chat streaming error: {str(e)}")
            raise
    
    def get_model_string(self) -> str:
        """Get the model string for Pydantic AI."""
        # For Pydantic AI compatibility
        return f"gemini:{self.model}"


class GeminiEmbeddingProvider(BaseEmbeddingProvider):
    """Google Gemini embedding provider implementation."""
    
    # Model configurations
    MODEL_CONFIGS = {
        "models/text-embedding-004": {
            "max_input_tokens": 2048,
            "supported_dimensions": [768, 1536, 3072],
            "default_dimension": 1536,
            "cost_per_1k_input": 0.000025,  # Update with actual pricing
        }
    }
    
    def __init__(
        self,
        model: str = "models/text-embedding-004",
        api_key: Optional[str] = None,
        dimensions: int = 1536,
        **kwargs
    ):
        super().__init__(model, api_key, **kwargs)
        
        # Validate dimensions
        config = self.MODEL_CONFIGS.get(model, self.MODEL_CONFIGS["models/text-embedding-004"])
        if dimensions not in config["supported_dimensions"]:
            raise ValueError(
                f"Gemini embedding model {model} supports dimensions: "
                f"{config['supported_dimensions']}. Got: {dimensions}"
            )
        
        self.dimensions = dimensions
        self.api_key = api_key or os.getenv("GEMINI_API_KEY")
        
        if not self.api_key:
            raise ValueError("Gemini API key is required")
        
        # Initialize the Gemini API
        genai.configure(api_key=self.api_key)
        
        logger.info(f"Initialized Gemini embedding provider with model: {model}, dimensions: {dimensions}")
    
    @property
    def provider_type(self) -> ProviderType:
        """Get the provider type."""
        return ProviderType.GEMINI
    
    @property
    def model_info(self) -> ModelInfo:
        """Get information about the current model."""
        config = self.MODEL_CONFIGS.get(self.model, self.MODEL_CONFIGS["models/text-embedding-004"])
        
        return ModelInfo(
            name=self.model,
            provider=self.provider_type,
            capabilities=[ModelCapability.EMBEDDING],
            context_window=config["max_input_tokens"],
            embedding_dimensions=self.dimensions,
            cost_per_1k_input=config["cost_per_1k_input"]
        )
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def embed(self, request: EmbeddingRequest) -> EmbeddingResponse:
        """Generate embeddings."""
        # Convert input to list if it's a string
        texts = [request.input] if isinstance(request.input, str) else request.input
        
        embeddings = []
        total_tokens = 0
        
        try:
            # Process each text
            for text in texts:
                # Use the embedding model with specified dimensions
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    None,
                    lambda: genai.embed_content(
                        model=self.model,
                        content=text,
                        output_dimensionality=self.dimensions
                    )
                )
                
                # Extract embedding
                if result and 'embedding' in result:
                    embeddings.append(result['embedding'])
                    total_tokens += self.estimate_tokens(text)
                else:
                    raise ValueError(f"No embedding returned for text: {text[:50]}...")
            
            usage = {
                "prompt_tokens": total_tokens,
                "total_tokens": total_tokens
            }
            
            return EmbeddingResponse(
                embeddings=embeddings,
                usage=usage,
                model=self.model
            )
            
        except Exception as e:
            logger.error(f"Gemini embedding error: {str(e)}")
            raise
    
    async def embed_batch(
        self,
        texts: List[str],
        batch_size: int = 100
    ) -> List[List[float]]:
        """Generate embeddings for multiple texts in batches."""
        all_embeddings = []
        
        # Gemini has a different rate limit, adjust batch size if needed
        gemini_batch_size = min(batch_size, 100)  # Gemini's limit
        
        for i in range(0, len(texts), gemini_batch_size):
            batch = texts[i:i + gemini_batch_size]
            request = EmbeddingRequest(input=batch)
            response = await self.embed(request)
            all_embeddings.extend(response.embeddings)
            
            # Small delay to respect rate limits
            if i + gemini_batch_size < len(texts):
                await asyncio.sleep(0.1)
        
        return all_embeddings


# Register providers
from .base import provider_registry

provider_registry.register_llm_provider("gemini", GeminiLLMProvider)
provider_registry.register_embedding_provider("gemini", GeminiEmbeddingProvider)